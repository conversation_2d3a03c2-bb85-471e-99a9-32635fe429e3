<?php
const TEXTDOMAIN = 'bergets-theme';
require_once "inc/api-integration.php";
require_once "inc/api-admin.php";

/**
 * Sets up the theme.
 *
 * This function is responsible for setting up the theme by adding various theme supports,
 * enqueueing editor styles, and modifying default attachment display settings.
 *
 * @since 1.0.0
 */
if ( ! function_exists( 'starter_setup_theme' ) ) :
	function starter_setup_theme() {
		// Make theme available for translation: Translations can be filed in the /languages/ directory.
		load_theme_textdomain( TEXTDOMAIN, __DIR__ . '/languages' );

		// Theme Support.
		add_theme_support( 'title-tag' );
		add_theme_support( 'automatic-feed-links' );
		add_theme_support( 'post-thumbnails' );
		add_theme_support(
			'html5',
			array(
				'search-form',
				'comment-form',
				'comment-list',
				'gallery',
				'caption',
				'script',
				'style',
				'navigation-widgets',
			)
		);

		// Add support for Block Styles.
		add_theme_support( 'wp-block-styles' );

		// Add support for full and wide alignment.
		add_theme_support( 'align-wide' );

		// Enqueue editor styles.
		add_editor_style( 'build/style-index.css' );

		// Default Attachment Display Settings.
		update_option( 'image_default_align', 'none' );
		update_option( 'image_default_link_type', 'none' );
		update_option( 'image_default_size', 'large' );

		// Custom CSS-Styles of Wordpress Gallery.
		add_filter( 'use_default_gallery_style', '__return_false' );
	}
	add_action( 'after_setup_theme', 'starter_setup_theme' );

	// Disable Block Directory: https://github.com/WordPress/gutenberg/blob/trunk/docs/reference-guides/filters/editor-filters.md#block-directory
	remove_action( 'enqueue_block_editor_assets', 'wp_enqueue_editor_block_directory_assets' );
	remove_action( 'enqueue_block_editor_assets', 'gutenberg_enqueue_block_editor_assets_block_directory' );
endif;

/**
 * Initializes the custom image size for the hero background.
 */
function starter_init_img_size() {
    add_image_size('hero-bg', 2800, 1400);
}
add_action('init', 'starter_init_img_size');

/**
 * Registers the WP Bootstrap Navwalker class.
 * This function is hooked to the 'after_setup_theme' action.
 */
function starter_register_navwalker(){
    require_once get_template_directory() . '/inc/class-wp-bootstrap-navwalker.php';
}
add_action( 'after_setup_theme', 'starter_register_navwalker' );

/**
 * Adds a new color palette to the WordPress editor.
 *
 * This function defines an array of colors and assigns them to the editor color palette.
 * Each color is assigned a name, slug, and hex value.
 *
 * @since 1.0.0
 */
function my_theme_add_new_features() {
	$newColorPalette = [];
	$color_array =array(
		'#FFFFFF',
		'#F6F6F7',
		'#B6B7B9',
		'#A3ADBA',
		'#FFCC00',
		'#3DD471',
		'#6F9194',
		'#0CBE92',
		'#FF9B00',
		'#28AD58',
		'#FB6F3C',
		'#FA6A11',
		'#139576',
		'#FF3E41',
		'#FF002E',
		'#4A647E',
		'#0068FF',
		'#9400FF',
		'#8700E9',
		'#3D00FF',
		'#5A00CF',
		'#2A3845',
		'#202122',
		'#0D1217');
	$i = 1;
	foreach ($color_array as $key => $item){
		$newColorPalette[] = array(
			'name' => 'Color ' . $i,
			'slug' => 'editor-' . $i++,
			'color' => $item,
		);
	}
	add_theme_support( 'editor-color-palette', $newColorPalette);
}
add_action( 'after_setup_theme', 'my_theme_add_new_features' );

/**
 * Registers the footer menus as sidebars.
 */
function starter_widgets_init() {

    // Footer Menu 1
    register_sidebar(
        array(
            'name'          => 'Footer Menu 1',
            'id'            => 'footer_menu_1',
            'before_widget' => '',
            'after_widget'  => '',
            'before_title'  => '<h3 class="widget-title">',
            'after_title'   => '</h3>',
        )
    );

    // Footer Menu 2
    register_sidebar(
        array(
            'name'          => 'Footer Menu 2',
            'id'            => 'footer_menu_2',
            'before_widget' => '',
            'after_widget'  => '',
            'before_title'  => '<h3 class="widget-title">',
            'after_title'   => '</h3>',
        )
    );

    // Footer Menu 3
    register_sidebar(
        array(
            'name'          => 'Footer Menu 3',
            'id'            => 'footer_menu_3',
            'before_widget' => '',
            'after_widget'  => '',
            'before_title'  => '<h3 class="widget-title">',
            'after_title'   => '</h3>',
        )
    );

    // Footer Menu 4
    register_sidebar(
        array(
            'name'          => 'Footer Menu 4',
            'id'            => 'footer_menu_4',
            'before_widget' => '',
            'after_widget'  => '',
            'before_title'  => '<h3 class="widget-title">',
            'after_title'   => '</h3>',
        )
    );

}
add_action( 'widgets_init', 'starter_widgets_init' );

/**
 * Registers navigation menus.
 *
 * This function registers the main navigation menu and the footer menu using the WordPress function register_nav_menus().
 *
 * @since 1.0.0
 */
if( function_exists( 'register_nav_menus' ) ) {
    register_nav_menus(
        array(
            'main-menu'   => 'Main Navigation Menu',
            'footer-menu' => 'Footer Menu',
        )
    );
}

/**
 * Enqueues the necessary styles and scripts for the theme.
 *
 * @since 1.0.0
 */
function starter_scripts_loader() {
    $theme_version = wp_get_theme()->get( 'Version' );

    // 1. Styles.
    wp_enqueue_style( 'style', get_theme_file_uri( 'style.css' ), array(), filemtime(get_theme_file_path('style.css')), 'all' );
    wp_enqueue_style( 'main', get_theme_file_uri( 'build/style-index.css' ), array(), filemtime(get_theme_file_path('build/style-index.css')), 'all' ); // main.scss: Compiled Framework source + custom styles.

    // 2. Scripts.
    wp_enqueue_script( 'mainjs', get_theme_file_uri( 'build/index.js' ), array(), filemtime(get_theme_file_path('build/index.js')), true );
    wp_localize_script('mainjs', 'custom_loadmore_params', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('ajax-nonce')
    ));

}
add_action( 'wp_enqueue_scripts', 'starter_scripts_loader' );

/**
 * Add inline admin CSS
 */
function add_admin_inline_css() {
    echo '<style>
        .post-type-houses .column-primary {
            width: 25%;
        }
    </style>';
}
add_action('admin_head', 'add_admin_inline_css');

/**
 * Adds a custom block category to the list of all block categories.
 *
 * @param array $categories The array of block categories.
 * @return array The modified array of block categories.
 */
add_filter( 'block_categories_all' , function( $categories ) {
    $categories_tmp = [];
    foreach ($categories as $key => $category){
        if($key == 0){
            $categories_tmp[] = array(
                'slug'  => 'visionmate',
                'title' => 'VISIONMATE'
            );
        }
        $categories_tmp[] = $category;
    }
    return $categories_tmp;
} );

/**
 * Adds options pages for global settings in the WordPress theme.
 */
function register_options_pages() {

    // Check if function exists.
    if ( ! function_exists( 'acf_add_options_page' ) ) {
        return;
    }

    // Register top-level options page.
    acf_add_options_page(
        array(
            'page_title' => 'Global Settings',
            'menu_title' => 'Global Settings',
            'menu_slug'  => 'theme-settings',
            'capability' => 'edit_posts',
            'icon_url'   => 'dashicons-grid-view',
            'redirect'   => true,
        )
    );

    // Register sub-page: Footer.
    acf_add_options_sub_page(
        array(
            'page_title'  => 'Footer',
            'menu_title'  => 'Footer',           
            'parent_slug' => 'theme-settings',
        )
    );

    // Register sub-page: Team.
    acf_add_options_sub_page(
        array(
            'page_title'  => 'Team',
            'menu_title'  => 'Team',
            'parent_slug' => 'theme-settings',
        )
    );

    // Register sub-page: Offer single.
    acf_add_options_sub_page(
        array(
            'page_title'  => 'Offer single',
            'menu_title'  => 'Offer single',
            'parent_slug' => 'theme-settings',
        )
    );
}
add_action( 'acf/init', 'register_options_pages' );

/**
 * Registers custom Gutenberg blocks using Advanced Custom Fields (ACF).
 *
 * @return void
 */
function starter_gutenberg_blocks(){
    if (function_exists('acf_register_block_type')) {
        acf_register_block_type(array(
            'name' => 'vm_hero',
            'title' => __('Hero', TEXTDOMAIN),
            'render_template' => 'blocks/hero/hero.php',
            'category' => 'visionmate',
            'icon' => 'editor-table',
        ));
        acf_register_block_type(array(
            'name' => 'vm_contact',
            'title' => __('Contact', TEXTDOMAIN),
            'render_template' => 'blocks/contact/contact.php',
            'category' => 'visionmate',
            'icon' => 'editor-table',
        ));
        acf_register_block_type(array(
            'name' => 'vm_offerts_list',
            'title' => __('Offerts List', TEXTDOMAIN),
            'render_template' => 'blocks/offerts-list/offerts-list.php',
            'category' => 'visionmate',
            'icon' => 'editor-table',
        ));
        acf_register_block_type(array(
            'name' => 'vm_team',
            'title' => __('Team', TEXTDOMAIN),
            'render_template' => 'blocks/team/team.php',
            'category' => 'visionmate',
            'icon' => 'editor-table',
        ));
        acf_register_block_type(array(
            'name' => 'vm_text',
            'title' => __('Text', TEXTDOMAIN),
            'render_template' => 'blocks/text/text.php',
            'category' => 'visionmate',
            'icon' => 'editor-table',
        ));
    }
}
add_action('acf/init', 'starter_gutenberg_blocks');

/**
 * Enables SVG upload support.
 *
 * This function adds the SVG and SVGZ file types to the list of allowed upload mime types.
 *
 * @param array $upload_mimes The array of allowed upload mime types.
 * @return array The updated array of allowed upload mime types.
 */
function starter_enable_svg_upload( $upload_mimes ) {
    $upload_mimes['svg'] = 'image/svg+xml';
    $upload_mimes['svgz'] = 'image/svg+xml';
    return $upload_mimes;
}
add_filter( 'upload_mimes', 'starter_enable_svg_upload', 10, 1 );

/**
 * Registers custom post types for Houses and Team.
 */
function starter_create_posttype() {
    register_post_type( 'houses',
        array(
            'labels' => array(
                'name' => __( 'Houses', TEXTDOMAIN ),
                'singular_name' => __( 'Houses', TEXTDOMAIN )
            ),
            'public' => true,
            'has_archive' => false,
            'rewrite' => array('slug' => 'houses', 'with_front' => false),
            'show_in_rest' => false,
            'supports' => array( 'title', 'editor', 'thumbnail' ),
        )
    );
    register_post_type( 'team',
        array(
            'labels' => array(
                'name' => __( 'Team', TEXTDOMAIN ),
                'singular_name' => __( 'Team', TEXTDOMAIN )
            ),
            'public' => true,
            'has_archive' => false,
            'rewrite' => array('slug' => 'team', 'with_front' => false),
            'show_in_rest' => false,
            'supports' => array( 'title', 'editor', 'author', 'thumbnail', 'excerpt', 'comments' ),
        )
    );
}
add_action( 'init', 'starter_create_posttype' );

/**
 * Registers the custom post status 'upcoming'.
 */
function custom_register_post_status() {
    register_post_status('upcoming', array(
        'label'                     => _x('Upcoming', 'post'),
        'label_count'               => _n_noop('Upcoming <span class="count">(%s)</span>', 'Upcoming <span class="count">(%s)</span>'),
        'public'                    => true,
        'exclude_from_search'       => true,
        'show_in_admin_all_list'    => true,
        'show_in_admin_status_list' => true
    ));
}
add_action('init', 'custom_register_post_status');

/**
 * Appends the 'Upcoming' label to posts with the 'upcoming' status.
 *
 * @param array   $post_states An array of post states.
 * @param WP_Post $post        The current post object.
 * @return array  The modified array of post states.
 */
function custom_append_post_status_label($post_states, $post) {
    if ($post->post_type === 'houses') {
        if ($post->post_status === 'upcoming') {
            $post_states['upcoming'] = __('Upcoming');
        }
    }
    return $post_states;
}
add_filter('display_post_states', 'custom_append_post_status_label', 10, 2);

/**
 * Adds the 'Upcoming' option to the quick edit screen in the WordPress admin.
 */
function custom_add_status_option_in_quick_edit() {
    ?>
    <script>
        jQuery(document).ready(function($) {
            $('select[name="_status"]').append('<option value="upcoming">Upcoming</option>');
        });
    </script>
    <?php
}
add_action('admin_footer-edit.php', 'custom_add_status_option_in_quick_edit');

/**
 * Adds the 'Upcoming' option to the post edit screens in the WordPress admin.
 */
function custom_add_status_option_in_post_page() {
    ?>
    <script>
        jQuery(document).ready(function($) {
            $('select[name="post_status"]').append('<option value="upcoming">Upcoming</option>');
        });
    </script>
    <?php
}
add_action('admin_footer-post.php', 'custom_add_status_option_in_post_page');
add_action('admin_footer-post-new.php', 'custom_add_status_option_in_post_page');

/**
 * Make houses of post status upcoming not indexable by search engines.
 */
function add_noindex_tag_to_custom_post_status() {
    global $post;

    // Check if it's a single post and has your custom post status
    if (is_singular() && $post->post_status == 'upcoming') {
        echo '<meta name="robots" content="noindex, nofollow">';
    }
}
remove_filter('wp_robots', 'wp_robots_max_image_preview_large');
add_action('wp_head', 'add_noindex_tag_to_custom_post_status');

/**
 * Registers custom taxonomies.
 */
function starter_create_taxonomy() {
    register_taxonomy(
        'profession',
        'team',
        register_taxonomy(
            'profession',
            'team',
            [
                'label' => __( 'Profession', TEXTDOMAIN ),
                'rewrite' => [ 'slug' => 'profession' ],
                'hierarchical' => true,
            ]
        )
    );
}
add_action( 'init', 'starter_create_taxonomy' );

/**
 * Retrieves the contents of an SVG file from a given URL.
 *
 * @param string $url The URL of the SVG file.
 * @return string The contents of the SVG file.
 */
function starter_get_icon_svg($url) {
    return file_get_contents($url);
}

/**
 * Adds a menu page for managing reusable blocks in the WordPress admin menu.
 *
 * This function registers a menu page with the title "Reusable Blocks" and the capability to edit posts.
 * The menu page is linked to the edit.php page with the post_type parameter set to wp_block.
 * The menu page uses the dashicons-editor-table icon and is positioned at position 22 in the admin menu.
 *
 * @since 1.0.0
 */
function starter_reusable_blocks_admin_menu() {
    add_menu_page( 'Reusable Blocks', 'Reusable Blocks', 'edit_posts', 'edit.php?post_type=wp_block', '', 'dashicons-editor-table', 22 );
}
add_action( 'admin_menu', 'starter_reusable_blocks_admin_menu' );

/**
 * Removes the 'wpautop' filter from the 'the_content' hook.
 * 
 * This function disables the automatic insertion of paragraph tags by the 'wpautop' filter
 * when displaying the content of a post or page.
 */
remove_filter( 'the_content', 'wpautop' );

/**
 * Adds custom columns to the houses post type in WordPress.
 *
 * @param array $columns The original columns.
 * @return array The modified columns.
 */
function add_custom_column_cpt($columns) {
    // Store the original date column
    $date_column_date = $columns['date'];
    
    // Remove unwanted columns
    unset($columns['date']);
    unset($columns['author']);
    
    // Add custom columns
    $columns['vm_property_type'] = __('Type', TEXTDOMAIN);
    $columns['vm_status'] = __('Status', TEXTDOMAIN);
    $columns['vm_address'] = __('Address', TEXTDOMAIN);
    $columns['vm_area'] = __('Area', TEXTDOMAIN);
    $columns['vm_municipality'] = __('Municipality', TEXTDOMAIN);
    // $columns['vm_rooms'] = __('Rooms', TEXTDOMAIN);
    // $columns['vm_living_space'] = __('Space', TEXTDOMAIN);
    // $columns['vm_price'] = __('Price', TEXTDOMAIN);
    // $columns['vm_img_status'] = __('Thumbnail', TEXTDOMAIN);
    
    // Restore the date column
    $columns['date'] = $date_column_date;
    
    // Return the modified columns
    return $columns;
}
add_filter( 'manage_houses_posts_columns', 'add_custom_column_cpt' );

/**
 * Displays custom columns for the houses post type in the admin panel.
 *
 * @param string $column The name of the column being displayed.
 * @param int $post_id The ID of the current post.
 * @return void
 */
function display_custom_column_cpt($column, $post_id) {
    $column_content = '';
    switch ($column) {
        case 'vm_property_type':
            $column_content = get_field('property_type', $post_id);
            break;
        case 'vm_status':
            $column_content = get_field('status_id', $post_id) . '=' . get_field('status', $post_id);
            break;
        case 'vm_address':
            $column_content = get_field('address', $post_id);
            break;
        case 'vm_area':
            $column_content = get_field('area', $post_id);
            break;
        case 'vm_municipality':
            $column_content = get_field('municipality', $post_id);
            break;
        // case 'vm_rooms':
        //     $column_content = get_field('rooms', $post_id);
        //     break;
        // case 'vm_living_space':
        //     $column_content = get_field('livingspace', $post_id);
        //     break;
        // case 'vm_price':
        //     $column_content = get_field('price', $post_id);
        //     break;
        // case 'vm_img_status':
        //     $column_content = (get_post_thumbnail_id($post_id)) ? '+' : '-';
        //     break;
    }
    echo $column_content;
}
add_action( 'manage_houses_posts_custom_column', 'display_custom_column_cpt', 10, 2 );

/**
 * Retrieves the minimum or maximum value of a specific ACF field from all published houses.
 *
 * @param string $field_name The name of the ACF field.
 * @param string $type The type of value to retrieve ('min' or 'max'). Default is 'min'.
 * @return mixed The minimum or maximum value of the ACF field, or null if no values are found.
 */
function min_max_acf_values($field_name, $type = 'min') {
    $args = array(
        'post_type' => 'houses',
        'post_status' => 'publish',
        'posts_per_page' => -1,
    );

    $query = new WP_Query($args);
    $values = array();

    if ( $query->have_posts() ) {
        while ( $query->have_posts() ) {
            $query->the_post();

            $acf_value = get_field($field_name, get_the_ID());
            if ($acf_value !== false) {
                $values[] = $acf_value;
            }
        }
    }

    wp_reset_postdata();

    $min_value = !empty($values) ? min($values) : null;
    $max_value = !empty($values) ? max($values) : null;

    $output = array(
        'min' => $min_value,
        'max' => $max_value,
    );

    if($field_name == 'rooms'){
        $output['max'] = 5;
    }

    if(is_null($output['min'])){
        $output['min'] = 0;
    }

    return $output[$type];
}

/**
 * Verifies the given nonce using wp_verify_nonce() function.
 * If the nonce is not valid, it terminates the script execution with an error message.
 *
 * @param string $nonce The nonce to be verified.
 * @return void
 */
function starter_verify_nonce($nonce) {
    if ( ! wp_verify_nonce( $nonce, 'ajax-nonce' ) )
        die ( 'Busted!');
}

/**
 * Retrieves the remaining estates via AJAX.
 *
 * This function fetches the remaining estates based on the provided query arguments and outputs the HTML markup for each estate.
 * It verifies the security nonce, builds the query arguments, adds broker search to the meta query, fetches the posts, and outputs the HTML markup for each estate.
 *
 * @return void
 */
function get_remaining_estates_ajax() {

    // Verify nonce for security
    starter_verify_nonce($_POST['nonce']);

    // Building the query args
    $args = [
        'numberposts'    => -1,
        'post_type'      => 'houses',
        'meta_key'       => 'price',
        'orderby'        => 'meta_value_num',
        'order'          => 'DESC',
        'post_status'    => 'publish',
        'fields'         => 'ids',
        'meta_query'     => [
            [
                'key'       => 'status_id',
                'value'     => [2, 10],
                'compare'   => 'IN',
                'type'      => 'NUMERIC',
            ]
        ],
    ];

    // Add broker search to meta query
    if($_POST['broker']) {
        $args['meta_query'][] = array(
            'key' => 'json_data',
            'value' => '"responsibleBroker": "' . $_POST['broker'] . '"',
            'compare' => 'LIKE',
            'type'    => 'TEXT',
        );
    }
    
    // Fetching the posts based on the args
    $results = get_posts($args);

    // Outputting the posts
    if(count($results)) {
        foreach ($results as $post) {
            $house_details = json_decode(get_field('json_data', $post), true);
            $property_type = get_field('property_type', $post);
            $status = $house_details['assignment']['status']['id'] ?? '';
            // update the status in case it has changed
            $status ?: update_field('status_id', $status, $post);
            switch($status) {
                case 3:
                    $category = 'till_salu';
                    break;
                case 2:
                    $category = 'kommande';
                    break;
                case 10:
                    $category = 'salda';
                    break;
                default:
                    $category = 'till_salu';
                    break;
            }
            if($status == 10) {
                $price = $house_details['price']['finalPrice'] ?? '';
            } else {
                $price = $house_details['price']['startingPrice'] ?? '';
            }
            if ($property_type == 'houses') {
                $rooms = $house_details['houseInterior']['numberOfRooms'] ?? '';
            } else {
                $rooms = $house_details['interior']['numberOfRooms'] ?? '';
            }
            $living_space = $house_details['baseInformation']['livingSpace'] ?? '';
            $area = $house_details['baseInformation']['objectAddress']['area'] ?? '';
            $address = $house_details['baseInformation']['objectAddress']['streetAddress'] ?? '';
            $municipality = $house_details['baseInformation']['objectAddress']['municipality'] ?? '';
            ?>
            <div 
            class="<?php echo 'js-house vm-col-12 vm-col-lg-6 vm-col-xl-6'; ?>" 
            data-category="<?php echo $category; ?>" 
            data-price="<?php echo trim($price); ?>" 
            data-rooms="<?php echo trim($rooms); ?>" 
            data-living-space="<?php echo trim($living_space); ?>"
            data-area="<?php echo trim($area); ?>"
            data-address="<?php echo trim($address); ?>"
            data-municipality="<?php echo trim($municipality); ?>"
            >
                <a href="<?php echo get_permalink($post); ?>">
                    <div class="vm-offerts-list__item">
                        <div class="vm-offerts-list__item-box">
                            <?php 
                            $image_id = get_post_thumbnail_id($post);
                            $extra_class = '';
                            if($image_id) {
                                $image_src = wp_get_attachment_image_src($image_id, 'hero-bg');
                                $image_width = $image_src[1];
                                $image_height = $image_src[2];
                                $image_orientation = ($image_width > $image_height) ? 'landscape' : 'portrait';
                                $extra_class .= ' ' . $image_orientation;
                                $image_url_smaller = wp_get_attachment_image_url($image_id, 'large');
                                // detect if thumbnail is a floor plan or not
                                $thumbnail_data = get_field('thumbnail_data');
                                $thumbnail_is_plan = (isset($thumbnail_data[0]['is_plan']) && $thumbnail_data[0]['is_plan'] == true) ? true : false;
                            }
                            if($thumbnail_is_plan == true) {
                                $extra_class .= ' plan';
                            }
                            ?>
                            <div class="vm-offerts-list__item-image">
                                <img 
                                data-src="<?php echo $image_url_smaller; ?>" 
                                class="<?php echo esc_attr($extra_class); ?>" 
                                >
                                <?php if($status == 10): ?>
                                    <span class="vm-badge">
                                        <span><?php echo __('SÅLD', TEXTDOMAIN); ?></span>
                                    </span>
                                <?php elseif($status == 2): ?>
                                    <span class="vm-badge vm-badge--alt">
                                        <span><?php echo __('KOMMANDE', TEXTDOMAIN); ?></span>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div class="vm-offerts-list__item-desc">
                                <div class="vm-grid">
                                    <div class="vm-col-6">
                                        <?php if($status == 10): ?>
                                            <?php if(isset($house_details['price']['finalPrice']) && !empty($house_details['price']['finalPrice'])): ?>
                                                <h5><?php echo __('Slutpris', TEXTDOMAIN ); ?></h5>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <?php if(isset($house_details['price']['startingPrice']) && !empty($house_details['price']['startingPrice'])): ?>
                                                <?php if (isset($house_details['price']['text']) && $house_details['price']['text'] == 'accepterat pris') : ?>
                                                    <h5><?php echo __('Accepterat pris', TEXTDOMAIN); ?></h5>
                                                <?php else : ?>
                                                    <h5><?php echo __('Utgångspris', TEXTDOMAIN); ?></h5>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                    <div class="vm-col-6">
                                        <?php if($status == 10): ?>
                                            <?php if(isset($house_details['price']['finalPrice']) && !empty($house_details['price']['finalPrice'])): ?>
                                                <h5><?php echo vm_number($house_details['price']['finalPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></h5>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <?php if(isset($house_details['price']['startingPrice']) && !empty($house_details['price']['startingPrice'])): ?>
                                                <h5><?php echo vm_number($house_details['price']['startingPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></h5>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="vm-flex">
                                    <?php if ($property_type == 'houses') : ?>
                                        <?php if (isset($house_details['baseInformation']['tenure']) && !empty($house_details['baseInformation']['tenure'])) : ?>
                                            <small><?php echo __('Upplåtelseform', TEXTDOMAIN); ?></small>
                                            <small><?php echo nl2br($house_details['baseInformation']['tenure']); ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['baseInformation']['tenure']) && !empty($house_details['baseInformation']['tenure'])) : ?>
                                            <small><?php echo __('Ägandeform', TEXTDOMAIN); ?></small>
                                            <small><?php echo nl2br($house_details['baseInformation']['tenure']); ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['houseInterior']['numberOfRooms']) && !empty($house_details['houseInterior']['numberOfRooms'])) : ?>
                                            <small><?php echo __('Rum', TEXTDOMAIN); ?></small>
                                            <small><?php echo nl2br($house_details['houseInterior']['numberOfRooms']); ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['houseInterior']['numberOffBedroom']) && !empty($house_details['houseInterior']['numberOffBedroom'])) : ?>
                                            <small><?php echo __('Varav sovrum', TEXTDOMAIN); ?></small>
                                            <small><?php 
                                            $bedroom_text = $house_details['houseInterior']['numberOffBedroom'];
                                            if (isset($house_details['houseInterior']['maxNumberOffBedroom']) && !empty($house_details['houseInterior']['maxNumberOffBedroom'])) :
                                                $bedroom_text .= ' - ' . $house_details['houseInterior']['maxNumberOffBedroom'];
                                            endif;
                                            echo nl2br($bedroom_text);
                                            ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])) : ?>
                                            <small><?php echo __('Boarea', TEXTDOMAIN); ?></small>
                                            <small><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])) : ?>
                                            <small><?php echo __('Biarea', TEXTDOMAIN); ?></small>
                                            <small><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['plot']['area']) && !empty($house_details['plot']['area'])) : ?>
                                            <small><?php echo __('Tomtarea', TEXTDOMAIN); ?></small>
                                            <small><?php echo nl2br($house_details['plot']['area']); ?> kvm</small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])) : ?>
                                            <small><?php echo __('Byggnadsår', TEXTDOMAIN); ?></small>
                                            <small><?php echo nl2br($house_details['building']['buildingYear']) ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['baseInformation']['propertyUnitDesignation']) && !empty($house_details['baseInformation']['propertyUnitDesignation'])) : ?>
                                            <small><?php echo __('Fastighetsbeteckning', TEXTDOMAIN); ?></small>
                                            <small><?php echo nl2br($house_details['baseInformation']['propertyUnitDesignation']); ?></small>
                                        <?php endif; ?>
                                    <?php elseif ($property_type == 'condominiums') : ?>
                                        <?php if(isset($house_details['interior']['numberOfRooms']) && !empty($house_details['interior']['numberOfRooms'])): ?>
                                            <small><?php echo __('Rum', TEXTDOMAIN ); ?></small>
                                            <small><?php echo nl2br($house_details['interior']['numberOfRooms']); ?></small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])): ?>
                                            <small><?php echo __('Boarea', TEXTDOMAIN ); ?></small>
                                            <small><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])): ?>
                                            <small><?php echo __('Biarea', TEXTDOMAIN ); ?></small>
                                            <small><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['baseInformation']['monthlyFee']) && !empty($house_details['baseInformation']['monthlyFee'])): 
                                        $text = '';
                                        $text .= vm_number($house_details['baseInformation']['monthlyFee']) . ' ' . __('kr', TEXTDOMAIN);
                                        ?>
                                            <small><?php echo __('Månadsavgift', TEXTDOMAIN); ?></small>
                                            <small><?php echo $text; ?></small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['floorAndElevator']['floor']) && $house_details['floorAndElevator']['floor'] !== null): ?>
                                            <small><?php echo __('Våningsplan', TEXTDOMAIN ); ?></small>
                                            <?php 
                                            $floor_text = $house_details['floorAndElevator']['floor'];
                                            if (isset($house_details['floorAndElevator']['totalNumberOfFloors']) && $house_details['floorAndElevator']['totalNumberOfFloors'] !== null && $house_details['floorAndElevator']['totalNumberOfFloors'] >= $house_details['floorAndElevator']['floor']) :
                                                $floor_text .= '/' . $house_details['floorAndElevator']['totalNumberOfFloors'];
                                            endif;
                                            ?>
                                            <small><?php echo nl2br($floor_text); ?></small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['floorAndElevator']['elevator']) && !empty($house_details['floorAndElevator']['elevator'])): ?>
                                            <small><?php echo __('Hiss', TEXTDOMAIN ); ?></small>
                                            <small><?php echo ($house_details['floorAndElevator']['elevator'] == 'No') ? 'Nej' : 'Ja' ?></small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['balconyPatio']['balcony']) && !empty($house_details['balconyPatio']['balcony'])): ?>
                                            <small><?php echo __('Balkong', TEXTDOMAIN); ?></small>
                                            <small><?php echo ($house_details['balconyPatio']['balcony']) ? 'Ja' : 'Nej'; ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['balconyPatio']['patio']) && !empty($house_details['balconyPatio']['patio'])) : ?>
                                            <small><?php echo __('Uteplats', TEXTDOMAIN); ?></small>
                                            <small><?php echo ($house_details['balconyPatio']['patio']) ? 'Ja' : 'Nej'; ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['balconyPatio']['parkingLot']) && !empty($house_details['balconyPatio']['parkingLot'])) : ?>
                                            <small><?php echo __('Bilplats', TEXTDOMAIN); ?></small>
                                            <small><?php echo ($house_details['balconyPatio']['parkingLot']) ? 'Ja' : 'Nej'; ?></small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])): ?>
                                            <small><?php echo __('Byggnadsår', TEXTDOMAIN ); ?></small>
                                            <small><?php echo nl2br($house_details['building']['buildingYear']) ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['baseInformation']['propertyUnitDesignation']) && !empty($house_details['baseInformation']['propertyUnitDesignation'])) : ?>
                                            <small><?php echo __('Fastighetsbeteckning', TEXTDOMAIN); ?></small>
                                            <small><?php echo nl2br($house_details['baseInformation']['propertyUnitDesignation']); ?></small>
                                        <?php endif; ?>
                                    <?php 
                                    // housingCooperativeses
                                    else : 
                                    ?>
                                        <?php if(isset($house_details['interior']['numberOfRooms']) && !empty($house_details['interior']['numberOfRooms'])): ?>
                                            <small><?php echo __('Rum', TEXTDOMAIN ); ?></small>
                                            <small><?php echo nl2br($house_details['interior']['numberOfRooms']); ?></small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])): ?>
                                            <small><?php echo __('Boarea', TEXTDOMAIN ); ?></small>
                                            <small><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])): ?>
                                            <small><?php echo __('Biarea', TEXTDOMAIN ); ?></small>
                                            <small><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['baseInformation']['monthlyFee']) && !empty($house_details['baseInformation']['monthlyFee'])): 
                                        $text = '';
                                        $text .= vm_number($house_details['baseInformation']['monthlyFee']) . ' ' . __('kr', TEXTDOMAIN);
                                        ?>
                                            <small><?php echo __('Månadsavgift', TEXTDOMAIN); ?></small>
                                            <small><?php echo $text; ?></small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['floorAndElevator']['floor']) && $house_details['floorAndElevator']['floor'] !== null): ?>
                                            <small><?php echo __('Våningsplan', TEXTDOMAIN ); ?></small>
                                            <?php 
                                            $floor_text = $house_details['floorAndElevator']['floor'];
                                            if (isset($house_details['floorAndElevator']['totalNumberOfFloors']) && $house_details['floorAndElevator']['totalNumberOfFloors'] !== null && $house_details['floorAndElevator']['totalNumberOfFloors'] >= $house_details['floorAndElevator']['floor']) :
                                                $floor_text .= '/' . $house_details['floorAndElevator']['totalNumberOfFloors'];
                                            endif;
                                            ?>
                                            <small><?php echo nl2br($floor_text); ?></small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['floorAndElevator']['elevator']) && !empty($house_details['floorAndElevator']['elevator'])): ?>
                                            <small><?php echo __('Hiss', TEXTDOMAIN ); ?></small>
                                            <small><?php echo ($house_details['floorAndElevator']['elevator'] == 'No') ? 'Nej' : 'Ja' ?></small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['balconyPatio']['balcony']) && !empty($house_details['balconyPatio']['balcony'])): ?>
                                            <small><?php echo __('Balkong', TEXTDOMAIN); ?></small>
                                            <small><?php echo ($house_details['balconyPatio']['balcony']) ? 'Ja' : 'Nej'; ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['balconyPatio']['patio']) && !empty($house_details['balconyPatio']['patio'])) : ?>
                                            <small><?php echo __('Uteplats', TEXTDOMAIN); ?></small>
                                            <small><?php echo ($house_details['balconyPatio']['patio']) ? 'Ja' : 'Nej'; ?></small>
                                        <?php endif; ?>
                                        <?php if (isset($house_details['balconyPatio']['parkingLot']) && !empty($house_details['balconyPatio']['parkingLot'])) : ?>
                                            <small><?php echo __('Bilplats', TEXTDOMAIN); ?></small>
                                            <small><?php echo ($house_details['balconyPatio']['parkingLot']) ? 'Ja' : 'Nej'; ?></small>
                                        <?php endif; ?>
                                        <?php if(isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])): ?>
                                            <small><?php echo __('Byggnadsår', TEXTDOMAIN ); ?></small>
                                            <small><?php echo nl2br($house_details['building']['buildingYear']) ?></small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="vm-offerts-list__item-title flex items-center">
                            <h2><?php echo get_the_title($post); ?></h2>
                            <?php if( 
                                isset($house_details['internetSettings']['bidSetting'])
                                && $house_details['internetSettings']['bidSetting'] !== 'DontShowBidding' // don't show if bids visibility is set to "DontShowBidding"
                                && isset($house_details['assignment']['status']['id'])
                                && $house_details['assignment']['status']['id'] != 10 // don't show bids if house is sold
                                && isset($house_details['bids']) 
                                && !empty($house_details['bids']) 
                            ) { ?>
                                <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M177.299 130.588c13.458 13.577 35.389 13.633 48.917.125l76.573-76.462c13.54-13.52 13.498-35.473-.093-48.942l-2.034-2.016c-4.443-4.403-11.609-4.39-16.036.03l-109.334 109.15c-4.432 4.424-4.45 11.598-.042 16.046zM254.743 212.751 76.229 391.015c.258.225.519.444.764.689l43.473 43.4c.25.249.474.514.703.776l178.514-178.265zM326.24 73.17l-84.459 84.318 112.533 112.346 84.461-84.319zM381.395 334.409l1.952 1.916c4.453 4.369 11.596 4.334 16.005-.079l109.321-109.408c4.45-4.454 4.433-11.676-.04-16.108l-2.072-2.053c-13.493-13.371-35.256-13.329-48.698.094l-76.685 76.577c-13.587 13.567-13.49 35.613.217 49.061zM55.829 412.897c-.255-.255-.485-.525-.718-.793l-45.804 45.74c-12.41 12.389-12.41 32.476 0 44.865 12.41 12.389 32.53 12.389 44.94 0l45.801-45.737c-.252-.22-.507-.434-.747-.674zM497 482h-23.168l-5.301-38.167a16.569 16.569 0 0 0-16.411-14.289H280.336a16.569 16.569 0 0 0-16.375 14.041L258.033 482h-23.44c-8.284 0-15 6.716-15 15s6.716 15 15 15H497c8.284 0 15-6.716 15-15s-6.715-15-15-15z" fill="#000000" opacity="1" data-original="#000000" class=""></path></g></svg>
                            <?php } ?>
                        </div>
                    </div>
                </a>
            </div>
            <?php
        }
    }
    wp_die();
}
add_action('wp_ajax_get_remaining_estates', 'get_remaining_estates_ajax');
add_action('wp_ajax_nopriv_get_remaining_estates', 'get_remaining_estates_ajax');

/**
 * This function is called before a contact form is sent.
 * It retrieves the form data, sanitizes it, and prepares it for sending to an API.
 *
 * @param array $contact_form The contact form data.
 * @param bool $abort A flag indicating whether to abort the form submission.
 * @param array $submission The form submission data.
 * @return void
 */
function contact_form_before_sent($contact_form, &$abort, $submission) {

    $customer_id = 'M22127';

    // get the form data and sanitize it
    $form_type = isset($_POST['form_type']) ? esc_html($_POST['form_type']) : 'normal';
    $timeslot_id = (isset($_POST['select-date'])) ? esc_html($_POST['select-date']) : '';
    $message = (isset($_POST['text-message'])) ? esc_html(trim($_POST['text-message'])) : false;
    $first_name = (isset($_POST['text-namn'])) ? esc_html($_POST['text-namn']) : '';
    $last_name = (isset($_POST['text-efternamn'])) ? esc_html($_POST['text-efternamn']) : '';
    $phone = (isset($_POST['text-telefonnummer'])) ? esc_html($_POST['text-telefonnummer']) : '';
    $email = (isset($_POST['email-epost'])) ? esc_html($_POST['email-epost']) : '';
    $offer_id = (isset($_POST['offer_id'])) ? esc_html($_POST['offer_id']) : '';

    // house form
    if($form_type == 'house') {
        
        // send data to Vitec API (only on PRD/UAT)
        if(!empty($offer_id) && is_production() === true) {

            // send interest
            $interest_data = [
                'updatePerson' => [
                    'firstName'     => $first_name,
                    'lastName'      => $last_name,
                    'cellPhone'     => $phone,
                    'customerId'    => $customer_id,
                    'email' => [
                        'emailAddress' => $email
                    ]
                ],
                'estateId'      => $offer_id,
                'notifyUser'    => 1
            ];
            if($message && !empty($message)) {
                $interest_data['interestNote'] = esc_html($message);
            }
            send_contact_to_house($interest_data);

            // add participant to viewing timeslot
            if($timeslot_id && !empty($timeslot_id)) {
                $viewing_id = explode('_', $timeslot_id)[0];
                $viewing_data = [
                    'updatePerson' => [
                        'firstName'     => $first_name,
                        'lastName'      => $last_name,
                        'cellPhone'     => $phone,
                        'customerId'    => $customer_id,
                        'email' => [
                            'emailAddress' => $email
                        ]
                    ]
                ];
                add_viewing_timeslot_participant($viewing_id, $timeslot_id, $viewing_data);
            }
            
        }

        // skip mail
        // add_filter( 'wpcf7_skip_mail', '__return_true' );
    } 
    
    // normal form
    // if($form_type == 'normal') {
    //     if($message && !empty($message)) {
    //         $api_message .= __('Meddelande:', TEXTDOMAIN) . ' ' . $message;
    //     }
    //     $api_message = esc_html($api_message);
    //     $api_data = [
    //         'firstName'     => $first_name,
    //         'lastName'      => $last_name,
    //         'telePhone'     => $phone,
    //         'note'          => $api_message,
    //         'customerId'    => $customer_id,
    //         'address' => [
    //             'streetAddress' => $address,
    //             'zipCode'       => $zip_code,
    //         ],
    //         'email' => [
    //             'emailAddress' => $email
    //         ]
    //     ];
    //     if(is_production() === true) {
    //         send_contact_normal($api_data);
    //     }
    // } 
    
    // // short form
    // if($form_type == 'short') {
    //     $api_data = [
    //         'firstName'     => $first_name,
    //         'lastName'      => $last_name,
    //         'customerId'    => $customer_id,
    //         'email' => [
    //             'emailAddress' => $email
    //         ]
    //     ];
    //     if(is_production() === true) {
    //         send_contact_normal($api_data);
    //     }
    // }

    // Debug
    // echo 'API data debug: ';
    // echo json_encode($api_data, true);
    // die();

}
add_action('wpcf7_before_send_mail', 'contact_form_before_sent', 10, 3);

/**
 * Adds a custom form tag to Contact Form 7 that includes the post ID as a hidden field.
 */
function add_post_id_field() {
    wpcf7_add_form_tag('offer_id', 'wpcf7_post_id_field_handler');
    wpcf7_add_form_tag('broker_email', 'wpcf7_broker_email_field_handler');
}
add_action('wpcf7_init', 'add_post_id_field');

/**
 * Callback function for the custom form tag that generates the hidden field with the post ID.
 *
 * @param array $tag The form tag details.
 * @return string The HTML code for the hidden field.
 */
function wpcf7_post_id_field_handler($tag) {
    $post_id = get_the_ID();
    $house_id = get_field('house_id', $post_id);
    $html = '<input type="hidden" name="offer_id" value="' . $house_id . '" />';
    return $html;
}

/**
 * Callback function for the custom form tag that generates the hidden field with the broker email address.
 *
 * @param array $tag The form tag details.
 * @return string The HTML code for the hidden field.
 */
function wpcf7_broker_email_field_handler($tag) {
    $house_details_json = get_field('json_data');
    if(!$house_details_json) return '';

    $house_details = ($house_details_json) ? json_decode($house_details_json, true) : [];
    $broker = $house_details['assignment']['responsibleBroker'];
    if(!$broker) return '';

    $user_id = get_team_id_offer($house_details['assignment']['responsibleBroker']);
    if(!$user_id) return '';

    $broker_email = trim(get_field('email', $user_id ));
    $html = '<input type="hidden" name="broker_email" value="' . $broker_email . '" />';
    return $html;
}

/**
 * Retrieves the broker post ID by his/her Vitec user ID.
 *
 * @param int $value The user ID.
 * @return int|false The post ID if found, false otherwise.
 */
function get_team_id_offer($value) {
    $args = array(
        'post_type' => 'team',
        'posts_per_page' => 1,
        'meta_key' => 'user_id',
        'meta_value' => $value
    );
    $posts = get_posts($args);
    if($posts && isset($posts[0])) {
        return $posts[0]->ID;
    }
    return false;
}

/**
 * Formats a number with thousand separators.
 *
 * @param int|float $number The number to format.
 * @return string The formatted number.
 */
function vm_number($number) {
    return number_format($number, 0, '.', ' ');
}

/**
 * Adds viewings API data to the global variable if the current post is a singular 'houses' post.
 *
 * This function checks if the current post is a singular 'houses' post type. If it is, it retrieves
 * the house ID using the Advanced Custom Fields (ACF) function `get_field()`. If a house ID is found,
 * it fetches the viewings data from the viewings API using the `get_viewings_api()` function and
 * decodes the JSON response. The decoded viewings data is then stored in the global `$GLOBALS['viewings']` variable.
 *
 * @return void
 */
function add_viewings_api_to_global() {
    if (!is_singular('houses')) {
        return;
    }
    
    $house_id = get_field('house_id', get_the_ID());
    if (!$house_id) {
        return;
    }

    $viewings_api = get_viewings_api($house_id);
    $viewings = ($viewings_api) ? json_decode($viewings_api, true) : [];
    $GLOBALS['viewings'] = $viewings;
}
add_action('wp', 'add_viewings_api_to_global');

/**
 * Modifies the select dropdown tag in a Contact Form 7 form.
 *
 * This function is a filter callback that is hooked to the 'wpcf7_form_tag' filter.
 * It is responsible for modifying the select dropdown tag with the name 'select-date'.
 * The function retrieves house details from the current post using the 'json_data' field.
 * It then iterates through the 'viewings' array in the house details and filters out past dates.
 * For each future viewing, it formats the start and end times and adds them to the select dropdown options.
 * Finally, it returns the modified tag with the updated values and labels.
 *
 * @param array $tag The original tag array.
 * @param array $unused Unused parameter.
 * @return array The modified tag array.
 */
function cf7_select_dropdown($tag, $unused) {
    if ( $tag['name'] != 'select-date' )
        return $tag;

    $house_id = get_field('house_id', get_the_ID());

    if (!$house_id) {
        return $tag;
    }

    // Get viewing details
    $viewings = isset($GLOBALS['viewings']) ? $GLOBALS['viewings'] : [];

    if (empty($viewings)) {
        return $tag;
    }

    $timeslots_aggregated = [];

    // Aggregate timeslots
    foreach ($viewings as $viewing) {

        if (!isset($viewing['timeSlots']))
            continue;

        // Itarate through timeslots
        $timeslots = $viewing['timeSlots'];
        if (empty($timeslots)) {
            continue;
        }

        foreach ($timeslots as $timeslot) {

            // Add to aggregated array
            $timeslots_aggregated[] = [
                'id'         => $timeslot['id'],
                'startsAt'   => $timeslot['startsAt'],
                'endsAt'     => $timeslot['endsAt'],
                'commentary' => $viewing['commentary'],
            ];
        }
    }

    // Sort aggregated timeslots by date
    if ($timeslots_aggregated && !empty($timeslots_aggregated)) {
        usort($timeslots_aggregated, function ($a, $b) {
            return strtotime($a['startsAt']) <=> strtotime($b['startsAt']);
        });
    }

    // Show only 4 first timeslots
    foreach ($timeslots_aggregated as $timeslot) {

        // Get date and time
        $start_timestamp = strtotime($timeslot['startsAt']);
        $end_timestamp = strtotime($timeslot['endsAt']);

        // Compare starting date with current date, and only show future dates
        $now = current_time('timestamp');
        if ($start_timestamp < $now) {
            continue;
        }

        if ($end_timestamp - $start_timestamp == 86400) {
            $formatted_start_time = date_i18n('l - j F', $start_timestamp);
        } else {
            $formatted_start_time = date_i18n('l - j F - \k\l G:i', $start_timestamp);
            $formatted_end_time = date_i18n('G:i', $end_timestamp);
        }

        // Translate days and months
        $formatted_start_time = str_replace(
            array('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
            array('måndag', 'tisdag', 'onsdag', 'torsdag', 'fredag', 'lördag', 'söndag'),
            $formatted_start_time
        );
        $formatted_start_time = str_replace(
            array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November','December'),
            array('januari', 'februari', 'mars', 'april', 'maj', 'juni', 'juli', 'augusti', 'september', 'oktober', 'november','december'),
            $formatted_start_time
        );

        if ($end_timestamp - $start_timestamp == 86400) {
            $time = $formatted_start_time;
        } else {
            $time = $formatted_start_time . ' - ' . $formatted_end_time;
        }

        // Add timeslot to tag
        $tag['raw_values'][] = $timeslot['id'];
        $timeslot_range = $time;
        $tag['labels'][] = $timeslot_range;
    }

    // Add to select field
    $pipes = new WPCF7_Pipes($tag['raw_values']);
    $tag['values'] = $pipes->collect_befores();
    $tag['pipes'] = $pipes;

    return $tag;
}
add_filter( 'wpcf7_form_tag', 'cf7_select_dropdown', 10, 2); 

/**
 * Remove the enum validation for checkboxes in Contact Form 7 (this was breaking select field).
 */
remove_action( 'wpcf7_swv_create_schema', 'wpcf7_swv_add_select_enum_rules', 20, 2 );

/**
 * Adds inline styles to the theme. This code was originally added using Code Snippets plugin - kept here to not break things.
 *
 * @since 1.0.0
 */
function add_inline_styles() {
    $inline_styles = '';
    $inline_styles .= '<style>';
    $inline_styles .= '
    // Contact form
    .vm-contact__heading {
        font-size: 45px;
    }
    @media (min-width: 1199px) {
        .vm-contact__text {
            margin-bottom: 50px;
        }
    }

    // Hero
    @media (min-width: 992px) {
        .vm-header__menu .menu-item a {
            transition: 0.2s;
            position: relative;
            overflow: hidden;
            display: block;
        }
        .vm-header__menu .menu-item a::after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            border-bottom: 1px solid #000;
            width: 100%;
            transform: translatex(-100%);
            transition: 0.2s;
        }
        .vm-header__menu .menu-item a:hover::after  {
            transform: translatex(0)
        }
    }

    // Search
    .vm-header__search-form .asl_w_container {
        max-width: 100%;
        width: 100%;
        min-width: calc(100vw - 20px)
    }
    div.asl_r.vertical {
        border-radius: 20px;
        border: 1px solid #000;
    }
    div.asl_r .results .item .asl_content {
        padding: 10px;
    }
    div.asl_r .results .item .asl_content h3 a {
        color: #000;
        text-transform: uppercase;
        transition: 0.2s;
        font-family: Kunst Grotesk, sans-serif
    }
    div.asl_r .results .item .asl_content h3 a:hover {
        color: #9B3E31;
        font-family: Kunst Grotesk, sans-serif
    }
    @media (min-width: 576px) {
        .vm-header__search-form .asl_w_container {
            min-width: 420px;
        }
    }
    @media (min-width: 992px) {
        .vm-header__search-form{
        right: 70px;
        top: -14px;
        }
    }

    // Mobile menu
    .menu-main-menu-container .menu-main-menu .menu-item{
        text-align: left;
    }

    // Property Page

    /* Offer description */
    .vm-single-offerts__content {
        padding: 36px 0;
    }

    /* Offer table */
    .vm-single-offerts .vm-tabs .tablink-mobile {
        font-family: "Kunst Grotesk", sans-serif
    }

    /* Contact section */
    .vm-contact.vm-contact--team {
        padding: 33px 0;
    }
    .vm-contact-team__image {
        border-radius: 20px;
    }
    .vm-contact-team__meta {
        margin-top: 15px;
    }

    @media (min-width: 992px) { 
        
        /* Description */
        .vm-single-offerts .vm-tabs .tabcontent {
            margin-bottom: 80px;
        }
        
        /* Table */
        .vm-single-offerts .vm-tabs {
            margin-bottom: 0;
        }
        .vm-single-offerts .vm-tabs .tablink {
            margin-right: 10px;
            margin-bottom: 10px;
            border: 1px solid #9B3E31;
            color: #9B3E31;
        }
        .vm-single-offerts .vm-tabs .tablink:hover {
            background-color: #9B3E31;
            color: #fff;
        }

    }

    @media (min-width: 1199px) { 
        .vm-single-offerts__short-desc {
            width: 80%;
        }
    }

    // Filters
    .vm-offerts-list__filters {
        padding-top: 15px;
    }
    #offer_price, #offer_rooms, #offer_area {
        margin-top: 15px;
        margin-left: 10px;
        margin-right: 10px;
    }
    @media (min-width: 992px) {
        #offer_price, #offer_rooms, #offer_area {
            margin-top: 25px;
        }
    }
    .vm-offerts-list .vm-badge {
        width: 80px;
        height: 40px;
        border-top-left-radius: 80px;
        border-top-right-radius: 80px;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        font-size: 14px;
        padding: 12px;
        text-align: center;
        background-color: #141e16;
        color: #fff;
    }

    // Footer
    @media (min-width: 992px) {
        .vm-footer {
            padding: 45px 20px 46px 20px
        }
    }

    // Short contact form
    .vm-contact.vm-contact--short {
        padding: 50px 0;
    }

    // Properties section (home)
    .vm-offerts-list {
        padding-bottom: 78px;
    }

    // Team section

    /* Header */
    .vm-team__heading {
        max-width: 230px;
        margin-bottom: 29px;
    }

    /* List item */
    .vm-team__logo {
        max-width: 90px;
        bottom: -2px;
        display: none;
    }

    @media (min-width: 992px) {
        /* Header */
        .vm-team__heading {
            max-width: 370px;
        }
        
        /* List item */
        .vm-team__item {
            margin-bottom: 47px;
        }
    }

    @media (min-width: 1199px) {
        .vm-team__logo {
            display: block;
        }
    }';
    $inline_styles .= '</style>';
    echo $inline_styles;
}
add_action('wp_footer', 'add_inline_styles');

/**
 * Remove WP Mail SMTP Pro license notification 
 */
function remove_wp_mail_smtp_license_notification() {
  echo '<style>
    .wp-mail-smtp-license-notice {
      display: none!important;
    } 
  </style>';
}
add_action( 'admin_head', 'remove_wp_mail_smtp_license_notification' );

/**
 * Exclude houses that are not for sale from Ajax Search Lite results
 */
function modify_asl_query($args, $search_id, $options) {
    $houses_not_for_sale_args = [
        'numberposts'    => -1,
        'post_type'      => 'houses',
        'post_status'    => 'publish',
        'fields'         => 'ids',
        'meta_query'     => [
            [
                'key'       => 'status_id',
                'value'     => [3],
                'compare'   => 'NOT IN',
                'type'      => 'NUMERIC',
            ]
        ],
    ];
    $houses_not_for_sale_results = get_posts($houses_not_for_sale_args);
    if($houses_not_for_sale_results) {
        $args['post_not_in'] = $houses_not_for_sale_results;
    }
    return $args;
}
add_filter('asl_query_args', 'modify_asl_query', 10, 3);

/**
 * Fill in minicipality meta field for every house (it is $house_details['baseInformation']['objectAddress']['municipality'])
 */
function fill_in_municipality_meta_field() {
    $args = array(
        'post_type' => 'houses',
        'posts_per_page' => -1,
    );
    $query = new WP_Query($args);
    if ( $query->have_posts() ) {
        while ( $query->have_posts() ) {
            $query->the_post();

            $house_details = json_decode(get_field('json_data', get_the_ID()), true);
            if($house_details && isset($house_details['baseInformation']['objectAddress']['municipality']) && !empty($house_details['baseInformation']['objectAddress']['municipality'])) {
                update_field('municipality', $house_details['baseInformation']['objectAddress']['municipality'], get_the_ID());
            }
        }
    }
    wp_reset_postdata();
}
// add_action('init', 'fill_in_municipality_meta_field');

/**
 * Remove all houses with meta field status_id = 11 (kommande)
 */
function remove_wrong_houses() {

    // Only run this when GET param remove_kommande_houses is set
    if(!isset($_GET['remove_wrong_houses'])) {
        return;
    }

    $args = array(
        'post_type' => 'houses',
        'posts_per_page' => -1,
        'post_status' => 'any',
        'meta_query' => array(
            array(
                'key' => 'status_id',
                'value' => 11,
                'compare' => '=',
            )
        )
    );
    $query = new WP_Query($args);
    if ( $query->have_posts() ) {
        while ( $query->have_posts() ) {
            $query->the_post();

            $args = [
                'post_type' => 'attachment',
                'posts_per_page' => -1,
                'post_status' => 'any',
                'fields' => 'ids',
                'post_parent' => get_the_ID()
            ];
            $attachments = get_posts($args);
            foreach($attachments as $attachment) {
                wp_delete_attachment($attachment, true);
            }

            wp_delete_post(get_the_ID(), true);

        }
    }
    wp_reset_postdata();
}
add_action('init', 'remove_wrong_houses');

/**
 * Custom modified wp_trim_words function
 */
function custom_wp_trim_words($text, $num_words = 55, $more = null) {
    if (null === $more) {
        $more = __('...');
    }
    $original_text = $text;
    $num_words = (int) $num_words;
    $words_array = [];

    // Tokenize the string by words including punctuation marks
    $tokens = preg_split('/(<[^>]*>|\\b)/u', $text, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);
    $word_count = 0;
    $inside_tag = false;

    foreach ($tokens as $token) {
        // Check if we're inside an HTML tag
        if (strpos($token, '<') === 0) {
            $inside_tag = true;
        }

        // If not inside an HTML tag, count the words
        if (!$inside_tag && preg_match('/\b/u', $token)) {
            $word_count++;
        }

        // Add the token to the words array
        $words_array[] = $token;

        // Check if we've reached the word limit
        if ($word_count >= $num_words && !$inside_tag) {
            break;
        }

        // Check if we're closing an HTML tag
        if (strpos($token, '>') !== false) {
            $inside_tag = false;
        }
    }

    // If the number of words exceeds the limit, truncate the array and add more indicator
    if (count($words_array) > $num_words) {
        // Trim trailing spaces and line breaks before adding the ellipsis
        $last_word = rtrim(end($words_array), " \t\n\r\0\x0B");
        
        // Add the ellipsis after the last word
        $text = implode('', array_slice($words_array, 0, -1)) . $more;
    } else {
        $text = implode('', $words_array);
    }

    return apply_filters('wp_trim_words', $text, $num_words, $more, $original_text);
}

/**
 * Disable XML-RPC
 */
function remove_xmlrpc_methods($methods) {
    return array();
}
add_filter('xmlrpc_methods', 'remove_xmlrpc_methods');

/**
 * Get reading time for the current post
 */
function reading_time()
{
    $content = get_post_field('post_content', get_post());
    $word_count = str_word_count(strip_tags($content));
    $reading_time = ceil($word_count / 200); // 200 ord/minut
    return $reading_time;
}

/**
 * Add Vitec link to admin bar on property single pages
 */
function add_vitec_admin_bar_link($wp_admin_bar) {
    // Only show on single houses pages
    if (!is_singular('houses')) {
        return;
    }
    
    // Get the house_id ACF field
    $house_id = get_field('house_id', get_the_ID());
    
    // Only proceed if house_id exists
    if (!$house_id) {
        return;
    }
    
    // Build the Vitec URL
    $vitec_url = 'https://express.maklare.vitec.net/objekt/' . $house_id;
    
    // Add the admin bar menu item
    $wp_admin_bar->add_menu(array(
        'id' => 'vitec-link',
        'parent' => false,
        'title' => __('Öppna i Vitec', TEXTDOMAIN),
        'href' => $vitec_url,
        'meta' => array(
            'target' => '_blank',
            'class' => 'vitec-admin-bar-link'
        )
    ));
}
add_action('admin_bar_menu', 'add_vitec_admin_bar_link', 100);

/**
 * AJAX handler for force update property
 */
function ajax_force_update_property() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'force_update_property_nonce')) {
        wp_die('Security check failed');
    }
    
    // Check if user has permission
    if (!current_user_can('edit_posts')) {
        wp_die('Insufficient permissions');
    }
    
    // Get the post ID
    $post_id = intval($_POST['post_id']);
    
    if (!$post_id) {
        wp_send_json_error('Invalid post ID');
    }
    
    // Check if the post exists and user can edit it
    $post = get_post($post_id);
    if (!$post || !current_user_can('edit_post', $post_id)) {
        wp_send_json_error('Post not found or insufficient permissions');
    }
    
    // Update the date_change ACF field to force update from API
    $update_result = update_field('date_change', '1900-01-01 00:00:00', $post_id);
    
    wp_send_json_success();
}
add_action('wp_ajax_force_update_property', 'ajax_force_update_property');

/**
 * AJAX handler for fix property images
 */
function ajax_fix_property_images() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'force_update_property_nonce')) {
        wp_die('Security check failed');
    }
    
    // Check if user has permission
    if (!current_user_can('edit_posts')) {
        wp_die('Insufficient permissions');
    }
    
    // Get the post ID
    $post_id = intval($_POST['post_id']);
    
    if (!$post_id) {
        wp_send_json_error('Invalid post ID');
    }
    
    // Check if the post exists and user can edit it
    $post = get_post($post_id);
    if (!$post || !current_user_can('edit_post', $post_id)) {
        wp_send_json_error('Post not found or insufficient permissions');
    }
    
    // Update the main date_change ACF field to past date
    update_field('date_change', '1900-01-01 00:00:00', $post_id);
    
    // Get current repeater data
    $thumbnail_data = get_field('thumbnail_data', $post_id);
    $gallery_data = get_field('gallery_data', $post_id);
    $plans_data = get_field('plans_data', $post_id);
    $documents_data = get_field('documents', $post_id);
    
    // Update thumbnail_data repeater dates
    if (is_array($thumbnail_data)) {
        foreach ($thumbnail_data as &$thumbnail) {
            $thumbnail['date_change'] = '1900-01-01 00:00:00';
        }
        update_field('thumbnail_data', $thumbnail_data, $post_id);
    }
    
    // Update gallery_data repeater dates
    if (is_array($gallery_data)) {
        foreach ($gallery_data as &$gallery_item) {
            $gallery_item['date_change'] = '1900-01-01 00:00:00';
        }
        update_field('gallery_data', $gallery_data, $post_id);
    }
    
    // Update plans_data repeater dates
    if (is_array($plans_data)) {
        foreach ($plans_data as &$plan) {
            $plan['date_change'] = '1900-01-01 00:00:00';
        }
        update_field('plans_data', $plans_data, $post_id);
    }
    
    // Update documents repeater dates
    if (is_array($documents_data)) {
        foreach ($documents_data as &$document) {
            $document['date_change'] = '1900-01-01 00:00:00';
        }
        update_field('documents', $documents_data, $post_id);
    }
    
    wp_send_json_success();
}
add_action('wp_ajax_fix_property_images', 'ajax_fix_property_images');

/**
 * Show notice when force_update or fix_images parameter is present in URL
 */
function show_force_update_notice() {
    // Only on houses edit pages
    if (!is_admin()) {
        return;
    }
    
    // Handle force update notice
    if (isset($_GET['force_update']) && $_GET['force_update'] == '1') {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p>Fastigheten har markerats för uppdatering från API:n.</p>';
            echo '</div>';
        });
    }
    
    // Handle fix images notice
    if (isset($_GET['fix_images']) && $_GET['fix_images'] == '1') {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p>Fastighetens bilder har markerats för ombyggnad. De kommer att fixas under nästa 3-minuters uppdatering.</p>';
            echo '</div>';
        });
    }
}
add_action('admin_init', 'show_force_update_notice');

/**
 * Add property management metabox to houses edit page
 */
function add_property_management_metabox() {
    add_meta_box(
        'property_management_actions',
        __('Fastighetshantering', TEXTDOMAIN),
        'render_property_management_metabox',
        'houses',
        'side',
        'default'
    );
}
add_action('add_meta_boxes', 'add_property_management_metabox');

/**
 * Remove WP Rocket Options metabox from single post edit page
 */
function remove_wp_rocket_metabox() {
    // Remove WP Rocket metabox from regular posts
    remove_meta_box('rocket_post_exclude', 'post', 'side');
    
    // Remove WP Rocket metabox from pages
    remove_meta_box('rocket_post_exclude', 'page', 'side');
    
    // Remove WP Rocket metabox from houses custom post type
    remove_meta_box('rocket_post_exclude', 'houses', 'side');
    
    // Remove WP Rocket metabox from team custom post type
    remove_meta_box('rocket_post_exclude', 'team', 'side');
}
add_action('add_meta_boxes', 'remove_wp_rocket_metabox', 99);

/**
 * Render the property management metabox
 */
function render_property_management_metabox($post) {
    // Get the house_id ACF field
    $house_id = get_field('house_id', $post->ID);
    
    // Only proceed if house_id exists
    if (!$house_id) {
        echo '<p>' . __('Inget hus-ID hittades för denna fastighet.', TEXTDOMAIN) . '</p>';
        return;
    }
    
    // Build the Vitec URL
    $vitec_url = 'https://express.maklare.vitec.net/objekt/' . $house_id;
    
    // Generate nonce for AJAX security
    $nonce = wp_create_nonce('force_update_property_nonce');
    $post_id = $post->ID;
    
    echo '<div class="property-management-actions" data-post-id="' . $post_id . '" data-nonce="' . $nonce . '">';
    echo '<div class="action-buttons">';
    
    // Vitec link button
    echo '<a href="' . esc_url($vitec_url) . '" target="_blank" class="button">' . __('Öppna i Vitec', TEXTDOMAIN) . '</a>';
    echo '<p class="description">' . __('Öppna denna fastighet i Vitec Express.', TEXTDOMAIN) . '</p>';
    
    // Force update property button
    echo '<button type="button" class="button js-force-update-property">' . __('Tvinga uppdatering av fastighet', TEXTDOMAIN) . '</button>';
    echo '<p class="description">' . __('Tvinga uppdatering av fastighet om data saknas eller är felaktig.', TEXTDOMAIN) . '</p>';
    
    // Fix images button
    echo '<button type="button" class="button js-fix-property-images">' . __('Fixa alla bilder', TEXTDOMAIN) . '</button>';
    echo '<p class="description">' . __('Återbygg och fixa alla fastighetsbilder. Använd detta om bilder saknas eller är korrupta.', TEXTDOMAIN) . '</p>';
    
    echo '</div>';
    echo '</div>';
    
    // Add JavaScript for AJAX functionality
    echo '<script>
        jQuery(document).ready(function($) {
            $(document).on("click", ".js-force-update-property", function(e) {
                e.preventDefault();
                
                var $metabox = $(this).closest(".property-management-actions");
                var postId = $metabox.data("post-id");
                var nonce = $metabox.data("nonce");
                var $button = $(this);
                
                // Disable button during request
                $button.prop("disabled", true).text("' . __('Uppdaterar...', TEXTDOMAIN) . '");
                
                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "force_update_property",
                        post_id: postId,
                        nonce: nonce
                    },
                    success: function(response) {
                        // Get current URL and add parameters
                        var currentUrl = window.location.href;
                        var separator = currentUrl.indexOf("?") > -1 ? "&" : "?";
                        var newUrl = currentUrl + separator + "force_update=1&post_id=" + postId;
                        
                        // Reload page with parameters
                        window.location.href = newUrl;
                    },
                    error: function() {
                        alert("Ett oväntat fel inträffade. Försök igen.");
                        // Re-enable button on error
                        $button.prop("disabled", false).text("' . __('Tvinga uppdatering av fastighet', TEXTDOMAIN) . '");
                    }
                });
            });
            
            $(document).on("click", ".js-fix-property-images", function(e) {
                e.preventDefault();
                
                var $metabox = $(this).closest(".property-management-actions");
                var postId = $metabox.data("post-id");
                var nonce = $metabox.data("nonce");
                var $button = $(this);
                
                // Disable button during request
                $button.prop("disabled", true).text("' . __('Fixar bilder...', TEXTDOMAIN) . '");
                
                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "fix_property_images",
                        post_id: postId,
                        nonce: nonce
                    },
                    success: function(response) {
                        // Get current URL and add parameters
                        var currentUrl = window.location.href;
                        var separator = currentUrl.indexOf("?") > -1 ? "&" : "?";
                        var newUrl = currentUrl + separator + "fix_images=1&post_id=" + postId;
                        
                        // Reload page with parameters
                        window.location.href = newUrl;
                    },
                    error: function() {
                        alert("Ett oväntat fel inträffade. Försök igen.");
                        // Re-enable button on error
                        $button.prop("disabled", false).text("' . __('Fixa alla bilder', TEXTDOMAIN) . '");
                    }
                });
            });
            
            // Handle notice dismissal and URL cleanup
            $(document).on("click", ".notice-dismiss", function() {
                $(this).closest(".notice").fadeOut();
            });
            
            // Clean up URL parameters if present
            if (window.location.search.includes("force_update=1") || window.location.search.includes("fix_images=1")) {
                // Parse current URL and remove parameters
                var url = new URL(window.location.href);
                url.searchParams.delete("force_update");
                url.searchParams.delete("fix_images");
                // Also remove post_id parameter if it exists (it was only used for the notice)
                url.searchParams.delete("post_id");
                window.history.replaceState({}, document.title, url.toString());
            }
        });
    </script>';
    
    // Add some basic styling
    echo '<style>
        .property-management-actions {
            margin: 0;
        }
        .property-management-actions .description {
            font-style: italic;
            color: #666;
            margin-top: 5px;
            margin-bottom: 15px;
            font-size: 12px;
        }
        .property-management-actions .description:last-child {
            margin-bottom: 5px;
        }
        .property-management-actions .action-buttons {
            padding-top: 10px;
        }
        .property-management-actions .action-buttons .button {
            width: 100%;
            text-align: center;
            margin-bottom: 3px;
        }
        .property-management-actions .button-primary {
            background: #0073aa;
            border-color: #0073aa;
        }
        .property-management-actions p .button {
            margin-bottom: 3px !important;
        }
        .postbox .inside .property-management-actions .action-buttons {
            margin-top: 0;
        }
        .js-force-update-property:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        /* Add dismiss button functionality */
        .notice.is-dismissible {
            position: relative;
        }
        
        .notice .notice-dismiss:before {
            content: "\f153";
            display: block;
            font: normal 16px/1 dashicons;
            speak: none;
            text-align: center;
            width: 16px;
            height: 16px;
            margin: 8px;
            position: absolute;
            top: 0;
            right: 0;
            cursor: pointer;
        }
    </style>';
}