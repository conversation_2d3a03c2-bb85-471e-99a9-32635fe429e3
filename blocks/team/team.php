<?php 
$fields = get_fields();
?>
<section class="vm-team" <?php if($fields['title']) { echo 'id="' . sanitize_title($fields['title']) . '"'; } ?>>
    <div class="vm-grid">
        <div class="vm-col-12">
            <?php if($fields['title']): ?>
                <h2 class="vm-team__heading" data-aos="fade-right"><?php echo $fields['title']; ?></h2>
            <?php endif; ?>
            <?php if(is_array($fields['team'])): ?>
                <div class="vm-team__items">
                    <div class="vm-grid">
                        <?php foreach ($fields['team'] as $item): ?>
                            <?php if( 'publish' === get_post_status( $item ) ): ?>
                                <div class="vm-col-6 vm-col-md-4 vm-col-lg-3">
                                    <div class="vm-team__item">
                                        <a href="<?php echo get_permalink($item); ?>" class="vm-team__item-image link" style="background-image: url(<?php echo get_the_post_thumbnail_url($item, 'large') ?>)"></a>
                                        <div class="vm-team__item-meta" >
                                            <small><?php echo get_the_title($item); ?></small>
                                            <small><?php 
                                                $terms = get_the_terms($item, 'profession');
                                                $profession = (isset($terms[0])) ? $terms[0]->name : '';
                                                $profession_text = '';
                                                if(!empty($profession) && strpos($profession, 'Fastighetsmäklare') !== false ) {
                                                    $profession_text .= __('Reg.', TEXTDOMAIN) . ' ';
                                                }
                                                $profession_text .= $profession;
                                                echo esc_html($profession_text); 
                                            ?></small>
                                            <?php if( get_field('reg_number', $item ) ) { ?>
                                                <small><a href="tel:<?php echo str_replace(' ', '', get_field('reg_number', $item )); ?>"><?php echo get_field('reg_number', $item ); ?></a></small>
                                            <?php } ?>
                                            <?php if( get_field('email', $item ) ) { ?>
                                                <small><a href="mailto:<?php echo get_field('email', $item ); ?>"><?php echo get_field('email', $item ); ?></a></small>
                                            <?php } ?>
                                            <?php if( get_field('link', $item) ) { ?>
                                                <small><a href="<?php echo get_field('link', $item)['url']; ?>" class="vm-team__link" target="<?php echo get_field('link', $item)['target']; ?>"><?php echo get_field('link', $item)['title']; ?></a></small>
                                                <div class="vm-team__logo"></div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
