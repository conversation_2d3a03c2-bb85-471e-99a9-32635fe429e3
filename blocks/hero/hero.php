<?php $fields = get_fields(); ?>
<section class="vm-hero <?php if(!empty($fields['smaller'])): ?> vm-hero--smaller <?php endif; ?>" style="<?php if(empty($fields['media_type'])): ?>background-image: url(<?php echo wp_get_attachment_image_url($fields['background'], 'full'); ?>)<?php endif; ?>">
    <?php if(isset($fields['media_type']) && isset($fields['background_video'])): ?>
        <video loop autoplay muted playsinline>
            <source src="<?php echo $fields['background_video']; ?>" type="video/mp4">
        </video>
    <?php endif; ?>
    <div class="vm-grid">
        <div class="vm-col-12 vm-col-lg-10 vm-col-xxl-6">
            <div class="vm-hero__wrapper">
                <?php if($fields['title']): ?>
                    <h2 class="vm-hero__heading" data-aos="fade-up" data-aos-delay="450"><?php echo $fields['title']; ?></h2>
                <?php endif; ?>

                <?php if($fields['description']): ?>
                    <p class="vm-hero__text" data-aos="fade-up" data-aos-delay="750"><?php echo $fields['description']; ?></p>
                <?php endif; ?>
                <?php if(is_array($fields['buttons'])): ?>
                    <div class="vm-hero__buttons" data-aos="fade-up" data-aos-delay="1050">
                        <?php foreach ($fields['buttons'] as $item): ?>
                            <a href="<?php echo $item['button']['url']; ?>" target="<?php echo $item['button']['target']; ?>" class="vm-button vm-button--light">
                                <?php echo $item['button']['title']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

    </div>
</section>
