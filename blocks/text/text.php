<?php $fields = get_fields(); ?>
<section class="vm-text" <?php if($fields['title']) { echo 'id="' . sanitize_title($fields['title']) . '"'; } ?>>
    <div class="vm-grid">
        <div class="vm-col-12 vm-col-md-12">
            <?php if($fields['title']): ?>
                <h2 class="vm-text__title" 
                    data-aos="fade-right"
                    data-aos-offset="200">
                    <?php echo $fields['title']; ?>
                </h2>
            <?php endif; ?>

            <?php if($fields['content']): ?>
                <div class="vm-text__content" 
                    data-aos="fade-right"
                    data-aos-offset="200">
                    <?php echo $fields['content']; ?>
                </div>
            <?php endif; ?>
            <?php if($fields['button']): ?>
                <a href="<?php echo $fields['button']['url']; ?>" target="<?php echo $fields['button']['target']; ?>" class="vm-button vm-button--light"
                    data-aos="fade-right"
                    data-aos-offset="200">
                    <?php echo $fields['button']['title']; ?>
                </a>
            <?php endif; ?>
            <div class="vm-text__video">
                <?php if($fields['background']): ?>
                <video loop autoplay muted playsinline>
                    <source src="<?php echo $fields['background']; ?>" type="video/mp4">
                </video>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
