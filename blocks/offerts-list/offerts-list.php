<?php 
$fields = get_fields();
$args = [
    'numberposts'    => 4,
    'post_type'      => 'houses',
    'meta_key'       => 'price',
    'orderby'        => 'meta_value_num',
    'order'          => 'DESC',
    'post_status'    => 'publish',
    'fields'         => 'ids',
    'meta_query'     => [
        'relation'  => 'AND',
        [
            'key'       => 'status_id',
            'value'     => [3],
            'compare'   => 'IN',
            'type'      => 'NUMERIC',
        ]
    ],
];
if( $fields['show_filters'] == true ) {
    $args['numberposts'] = -1;
}
$houses_list = get_posts($args);

?>
<section class="vm-offerts-list" id="offerts">

    <?php if(isset($fields['show_filters']) && $fields['show_filters'] == true): ?>
    <div class="vm-offerts-list__filters">
        <form class="vm-offerts-list__form" autocomplete="off">
            <div class="vm-grid">
                <div class="vm-col-12 vm-col-lg-6 vm-offerts-list__form-buttons">
                    <label class="vm-offerts-list__form-radio">
                        <input type="radio" name="offer_type" checked value="till_salu" <?php echo (isset($_GET['offer_type']) && $_GET['offer_type'] == 'till_salu') ? 'checked' : ''; ?>>
                        <span class="vm-button vm-button--dark">
                            <?php include get_template_directory() . '/assets/svg/spinner.svg'; ?>
                            <?php echo __('Till salu', TEXTDOMAIN); ?>
                        </span>
                    </label>
                    <label class="vm-offerts-list__form-radio disabled">
                        <input type="radio" name="offer_type" value="kommande" <?php echo (isset($_GET['offer_type']) && $_GET['offer_type'] == 'kommande') ? 'checked' : ''; ?>>
                        <span class="vm-button vm-button--dark">
                            <?php include get_template_directory() . '/assets/svg/spinner.svg'; ?>
                            <?php echo __('Kommande', TEXTDOMAIN); ?>
                        </span>
                    </label>
                    <label class="vm-offerts-list__form-radio disabled">
                        <input type="radio" name="offer_type" value="salda" <?php echo (isset($_GET['offer_type']) && $_GET['offer_type'] == 'salda') ? 'checked' : ''; ?>>
                        <span class="vm-button vm-button--dark">
                            <?php include get_template_directory() . '/assets/svg/spinner.svg'; ?>
                            <?php echo __('Sålda', TEXTDOMAIN); ?>
                        </span>
                    </label>
                </div>
                <div class="vm-col-12 vm-col-lg-6 vm-col-align-right vm-offerts-list__form-search">
                    <input type="search" name="offer_search" class="tagify--outside" placeholder="Sök område/adress...">
                </div>
            </div>
            <div class="vm-grid">
                <div class="vm-col-12 vm-col-lg-6">
                    <h4><?php echo __('Filters', TEXTDOMAIN); ?></h4>
                </div>
            </div>
            <div class="vm-grid">
                <div class="vm-col-12 vm-col-lg-4">
                    <div class="vm-offerts-list__form-slider__values">
                        <div class="slider-value slider-value__lower"><?php echo min_max_acf_values('price') ?></div>
                        <div class="slider-value slider-value__higher"><?php echo min_max_acf_values('price', 'max') ?></div>
                    </div>
                    <div id="offer_price" class="vm-offerts-list__form-slider" data-vmstep="100000" data-vmstart="<?php echo min_max_acf_values('price') ?>" data-vmend="<?php echo min_max_acf_values('price', 'max') ?>" data-vmsuffix="kr"></div>
                    <input id="offer_price_input" name="offer_price" type="hidden" value="<?php echo (isset($_GET['offer_price'])) ? urldecode($_GET['offer_price']) : ''; ?>">
                </div>
                <div class="vm-col-12 vm-col-lg-4">
                    <div class="vm-offerts-list__form-slider__values">
                        <div class="slider-value slider-value__lower"><?php echo min_max_acf_values('rooms') ?></div>
                        <div class="slider-value slider-value__higher"><?php echo min_max_acf_values('rooms', 'max') ?></div>
                    </div>
                    <div id="offer_rooms" class="vm-offerts-list__form-slider" data-vmstep="1" data-vmstart="<?php echo min_max_acf_values('rooms') ?>" data-vmend="<?php echo min_max_acf_values('rooms', 'max') ?>" data-vmsuffix="rum"></div>
                    <input id="offer_rooms_input" name="offer_rooms" type="hidden" value="<?php echo (isset($_GET['offer_rooms'])) ? urldecode($_GET['offer_rooms']) : ''; ?>">
                </div>
                <div class="vm-col-12 vm-col-lg-4">
                    <div class="vm-offerts-list__form-slider__values">
                        <div class="slider-value slider-value__lower"><?php echo min_max_acf_values('livingspace') ?></div>
                        <div class="slider-value slider-value__higher"><?php echo min_max_acf_values('livingspace', 'max') ?></div>
                    </div>
                    <div id="offer_area" class="vm-offerts-list__form-slider" data-vmstep="1" data-vmstart="<?php echo min_max_acf_values('livingspace') ?>" data-vmend="<?php echo min_max_acf_values('livingspace', 'max') ?>" data-vmsuffix="kvm"></div>
                    <input id="offer_area_input" name="offer_area" type="hidden" value="<?php echo (isset($_GET['offer_area'])) ? urldecode($_GET['offer_area']) : ''; ?>">
                </div>
            </div>
            <input type="hidden" name="offer_address" value="<?php echo (isset($_GET['offer_address'])) ? urldecode($_GET['offer_address']) : ''; ?>">
            <input type="hidden" name="offer_area_name" value="<?php echo (isset($_GET['offer_area_name'])) ? urldecode($_GET['offer_area_name']) : ''; ?>">
            <input type="hidden" name="offer_municipality" value="<?php echo (isset($_GET['offer_municipality'])) ? urldecode($_GET['offer_municipality']) : ''; ?>">
        </form>
    </div>
    <?php endif; ?>
    <div class="vm-grid">
        <div class="vm-col-12 vm-col-md-12">
            <?php if(is_array($houses_list)): ?>
            <div class="vm-offerts-list__items">
                <div class="no-results hidden">
                    <p class="text-normal"><?php echo __('Inga resultat hittades för valda filter', TEXTDOMAIN); ?></p>
                </div>
                <div class="vm-grid <?php echo (is_front_page()) ? '' : 'js-houses-list'?>" style="<?php if( $fields['show_filters'] == true ) { ?>min-height: 800px<?php } ?>">
                    <?php 
                    // Homepage block
                    if( $fields['show_filters'] !== true ) {
                        foreach ($houses_list as $item):
                            $house_details = json_decode(get_field('json_data', $item), true);
                            $property_type = get_field('property_type', $item);
                            $status = $house_details['assignment']['status']['id'] ?? '';
                            // update the status in case it has changed
                            $status ?: update_field('status_id', $status, $item);
                            ?>
                            <div class="<?php echo 'vm-col-12 vm-col-md-6 vm-col-xl-6'; ?>">
                                <a href="<?php echo get_permalink($item); ?>">
                                    <div class="vm-offerts-list__item">
                                        <div class="vm-offerts-list__item-box">
                                            <?php 
                                            $image_id = get_post_thumbnail_id($item);
                                            $image_src = wp_get_attachment_image_src($image_id, 'hero-bg');
                                            $image_width = $image_src[1];
                                            $image_height = $image_src[2];
                                            $image_orientation = ($image_width > $image_height) ? 'landscape' : 'portrait';
                                            $image_url_smaller = wp_get_attachment_image_url($image_id, 'large');
                                            // detect if thumbnail is a floor plan or not
                                            $thumbnail_data = get_field('thumbnail_data');
                                            $thumbnail_is_plan = (isset($thumbnail_data[0]['is_plan']) && $thumbnail_data[0]['is_plan'] == true) ? true : false;
                                            $extra_class = '';
                                            if($thumbnail_is_plan == true) {
                                                $extra_class = 'plan';
                                            }
                                            ?>
                                            <div class="vm-offerts-list__item-image">
                                                <img data-src="<?php echo $image_url_smaller; ?>" class="lazyload <?php echo esc_attr($extra_class); ?>" alt="<?php echo esc_attr(get_the_title()); ?>">
                                                <?php if($status == 10 ): ?>
                                                    <span class="vm-badge">
                                                        <span><?php echo __('SÅLD', TEXTDOMAIN); ?></span>
                                                    </span>
                                                <?php elseif($status == 2 ): ?>
                                                    <span class="vm-badge vm-badge--alt">
                                                        <span><?php echo __('KOMMANDE', TEXTDOMAIN); ?></span>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="vm-offerts-list__item-desc">
                                                <div class="vm-grid">
                                                    <div class="vm-col-6">
                                                        <?php if($status == 10): ?>
                                                            <?php if(isset($house_details['price']['finalPrice']) && !empty($house_details['price']['finalPrice'])): ?>
                                                                <h5><?php echo __('Slutpris', TEXTDOMAIN ); ?></h5>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <?php if(isset($house_details['price']['startingPrice']) && !empty($house_details['price']['startingPrice'])): ?>
                                                                <?php if (isset($house_details['price']['text']) && $house_details['price']['text'] == 'accepterat pris') : ?>
                                                                    <h5><?php echo __('Accepterat pris', TEXTDOMAIN); ?></h5>
                                                                <?php else : ?>
                                                                    <h5><?php echo __('Utgångspris', TEXTDOMAIN); ?></h5>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="vm-col-6">
                                                        <?php if($status == 10): ?>
                                                            <?php if(isset($house_details['price']['finalPrice']) && !empty($house_details['price']['finalPrice'])): ?>
                                                                <h5><?php echo vm_number($house_details['price']['finalPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></h5>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <?php if(isset($house_details['price']['startingPrice']) && !empty($house_details['price']['startingPrice'])): ?>
                                                                <h5><?php echo vm_number($house_details['price']['startingPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></h5>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="vm-flex">
                                                    <?php if ($property_type == 'houses') : ?>
                                                        <?php if (isset($house_details['baseInformation']['tenure']) && !empty($house_details['baseInformation']['tenure'])) : ?>
                                                            <small><?php echo __('Upplåtelseform', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['tenure']); ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['baseInformation']['tenure']) && !empty($house_details['baseInformation']['tenure'])) : ?>
                                                            <small><?php echo __('Ägandeform', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['tenure']); ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['houseInterior']['numberOfRooms']) && !empty($house_details['houseInterior']['numberOfRooms'])) : ?>
                                                            <small><?php echo __('Rum', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['houseInterior']['numberOfRooms']); ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['houseInterior']['numberOffBedroom']) && !empty($house_details['houseInterior']['numberOffBedroom'])) : ?>
                                                            <small><?php echo __('Varav sovrum', TEXTDOMAIN); ?></small>
                                                            <small><?php 
                                                            $bedroom_text = $house_details['houseInterior']['numberOffBedroom'];
                                                            if (isset($house_details['houseInterior']['maxNumberOffBedroom']) && !empty($house_details['houseInterior']['maxNumberOffBedroom'])) :
                                                                $bedroom_text .= ' - ' . $house_details['houseInterior']['maxNumberOffBedroom'];
                                                            endif;
                                                            echo nl2br($bedroom_text);
                                                            ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])) : ?>
                                                            <small><?php echo __('Boarea', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])) : ?>
                                                            <small><?php echo __('Biarea', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['plot']['area']) && !empty($house_details['plot']['area'])) : ?>
                                                            <small><?php echo __('Tomtarea', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['plot']['area']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])) : ?>
                                                            <small><?php echo __('Byggnadsår', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['building']['buildingYear']) ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['baseInformation']['propertyUnitDesignation']) && !empty($house_details['baseInformation']['propertyUnitDesignation'])) : ?>
                                                            <small><?php echo __('Fastighetsbeteckning', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['propertyUnitDesignation']); ?></small>
                                                        <?php endif; ?>
                                                    <?php elseif ($property_type == 'condominiums') : ?>
                                                        <?php if(isset($house_details['interior']['numberOfRooms']) && !empty($house_details['interior']['numberOfRooms'])): ?>
                                                            <small><?php echo __('Rum', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['interior']['numberOfRooms']); ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])): ?>
                                                            <small><?php echo __('Boarea', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])): ?>
                                                            <small><?php echo __('Biarea', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['monthlyFee']) && !empty($house_details['baseInformation']['monthlyFee'])): 
                                                        $text = '';
                                                        $text .= vm_number($house_details['baseInformation']['monthlyFee']) . ' ' . __('kr', TEXTDOMAIN);
                                                        ?>
                                                            <small><?php echo __('Månadsavgift', TEXTDOMAIN); ?></small>
                                                            <small><?php echo $text; ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['floorAndElevator']['floor']) && $house_details['floorAndElevator']['floor'] !== null): ?>
                                                            <small><?php echo __('Våningsplan', TEXTDOMAIN ); ?></small>
                                                            <?php 
                                                            $floor_text = $house_details['floorAndElevator']['floor'];
                                                            if (isset($house_details['floorAndElevator']['totalNumberOfFloors']) && $house_details['floorAndElevator']['totalNumberOfFloors'] !== null && $house_details['floorAndElevator']['totalNumberOfFloors'] >= $house_details['floorAndElevator']['floor']) :
                                                                $floor_text .= '/' . $house_details['floorAndElevator']['totalNumberOfFloors'];
                                                            endif;
                                                            ?>
                                                            <small><?php echo nl2br($floor_text); ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['floorAndElevator']['elevator']) && !empty($house_details['floorAndElevator']['elevator'])): ?>
                                                            <small><?php echo __('Hiss', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo ($house_details['floorAndElevator']['elevator'] == 'No') ? 'Nej' : 'Ja' ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['balconyPatio']['balcony']) && !empty($house_details['balconyPatio']['balcony'])): ?>
                                                            <small><?php echo __('Balkong', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['balcony']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['balconyPatio']['patio']) && !empty($house_details['balconyPatio']['patio'])) : ?>
                                                            <small><?php echo __('Uteplats', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['patio']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['balconyPatio']['parkingLot']) && !empty($house_details['balconyPatio']['parkingLot'])) : ?>
                                                            <small><?php echo __('Bilplats', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['parkingLot']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])): ?>
                                                            <small><?php echo __('Byggnadsår', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['building']['buildingYear']) ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['baseInformation']['propertyUnitDesignation']) && !empty($house_details['baseInformation']['propertyUnitDesignation'])) : ?>
                                                            <small><?php echo __('Fastighetsbeteckning', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['propertyUnitDesignation']); ?></small>
                                                        <?php endif; ?>
                                                    <?php 
                                                    // housingCooperativeses
                                                    else : 
                                                    ?>
                                                        <?php if(isset($house_details['interior']['numberOfRooms']) && !empty($house_details['interior']['numberOfRooms'])): ?>
                                                            <small><?php echo __('Rum', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['interior']['numberOfRooms']); ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])): ?>
                                                            <small><?php echo __('Boarea', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])): ?>
                                                            <small><?php echo __('Biarea', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['monthlyFee']) && !empty($house_details['baseInformation']['monthlyFee'])): 
                                                        $text = '';
                                                        $text .= vm_number($house_details['baseInformation']['monthlyFee']) . ' ' . __('kr', TEXTDOMAIN);
                                                        ?>
                                                            <small><?php echo __('Månadsavgift', TEXTDOMAIN); ?></small>
                                                            <small><?php echo $text; ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['floorAndElevator']['floor']) && $house_details['floorAndElevator']['floor'] !== null): ?>
                                                            <small><?php echo __('Våningsplan', TEXTDOMAIN ); ?></small>
                                                            <?php 
                                                            $floor_text = $house_details['floorAndElevator']['floor'];
                                                            if (isset($house_details['floorAndElevator']['totalNumberOfFloors']) && $house_details['floorAndElevator']['totalNumberOfFloors'] !== null && $house_details['floorAndElevator']['totalNumberOfFloors'] >= $house_details['floorAndElevator']['floor']) :
                                                                $floor_text .= '/' . $house_details['floorAndElevator']['totalNumberOfFloors'];
                                                            endif;
                                                            ?>
                                                            <small><?php echo nl2br($floor_text); ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['floorAndElevator']['elevator']) && !empty($house_details['floorAndElevator']['elevator'])): ?>
                                                            <small><?php echo __('Hiss', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo ($house_details['floorAndElevator']['elevator'] == 'No') ? 'Nej' : 'Ja' ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['balconyPatio']['balcony']) && !empty($house_details['balconyPatio']['balcony'])): ?>
                                                            <small><?php echo __('Balkong', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['balcony']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['balconyPatio']['patio']) && !empty($house_details['balconyPatio']['patio'])) : ?>
                                                            <small><?php echo __('Uteplats', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['patio']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['balconyPatio']['parkingLot']) && !empty($house_details['balconyPatio']['parkingLot'])) : ?>
                                                            <small><?php echo __('Bilplats', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['parkingLot']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])): ?>
                                                            <small><?php echo __('Byggnadsår', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['building']['buildingYear']) ?></small>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="vm-offerts-list__item-title flex items-center">
                                            <h2><?php echo get_the_title($item); ?></h2>
                                            <?php if( 
                                                isset($house_details['internetSettings']['bidSetting'])
                                                && $house_details['internetSettings']['bidSetting'] !== 'DontShowBidding' // don't show if bids visibility is set to "DontShowBidding"
                                                && isset($house_details['assignment']['status']['id'])
                                                && $house_details['assignment']['status']['id'] != 10 // don't show bids if house is sold
                                                && isset($house_details['bids']) 
                                                && !empty($house_details['bids']) 
                                            ) { ?>
                                                <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M177.299 130.588c13.458 13.577 35.389 13.633 48.917.125l76.573-76.462c13.54-13.52 13.498-35.473-.093-48.942l-2.034-2.016c-4.443-4.403-11.609-4.39-16.036.03l-109.334 109.15c-4.432 4.424-4.45 11.598-.042 16.046zM254.743 212.751 76.229 391.015c.258.225.519.444.764.689l43.473 43.4c.25.249.474.514.703.776l178.514-178.265zM326.24 73.17l-84.459 84.318 112.533 112.346 84.461-84.319zM381.395 334.409l1.952 1.916c4.453 4.369 11.596 4.334 16.005-.079l109.321-109.408c4.45-4.454 4.433-11.676-.04-16.108l-2.072-2.053c-13.493-13.371-35.256-13.329-48.698.094l-76.685 76.577c-13.587 13.567-13.49 35.613.217 49.061zM55.829 412.897c-.255-.255-.485-.525-.718-.793l-45.804 45.74c-12.41 12.389-12.41 32.476 0 44.865 12.41 12.389 32.53 12.389 44.94 0l45.801-45.737c-.252-.22-.507-.434-.747-.674zM497 482h-23.168l-5.301-38.167a16.569 16.569 0 0 0-16.411-14.289H280.336a16.569 16.569 0 0 0-16.375 14.041L258.033 482h-23.44c-8.284 0-15 6.716-15 15s6.716 15 15 15H497c8.284 0 15-6.716 15-15s-6.715-15-15-15z" fill="#000000" opacity="1" data-original="#000000" class=""></path></g></svg>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php 
                        endforeach;
                    }

                    // Estates list
                    else {
                        foreach ($houses_list as $item):
                            $house_details = json_decode(get_field('json_data', $item), true);
                            $property_type = get_field('property_type', $item);
                            $status = $house_details['assignment']['status']['id'] ?? '';
                            // update the status in case it has changed
                            $status ?: update_field('status_id', $status, $item);
                            switch($status) {
                                case 3:
                                    $category = 'till_salu';
                                    break;
                                case 2:
                                    $category = 'kommande';
                                    break;
                                case 10:
                                    $category = 'salda';
                                    break;
                                default:
                                    $category = 'none';
                                    break;
                            }
                            if($status == 10) {
                                $price = $house_details['price']['finalPrice'] ?? '';
                            } else {
                                $price = $house_details['price']['startingPrice'] ?? '';
                            }
                            if ($property_type == 'houses') {
                                $rooms = $house_details['houseInterior']['numberOfRooms'] ?? '';
                            } else {
                                $rooms = $house_details['interior']['numberOfRooms'] ?? '';
                            }
                            $living_space = $house_details['baseInformation']['livingSpace'] ?? '';
                            $area = $house_details['baseInformation']['objectAddress']['area'] ?? '';
                            $address = $house_details['baseInformation']['objectAddress']['streetAddress'] ?? '';
                            $municipality = $house_details['baseInformation']['objectAddress']['municipality'] ?? '';
                            ?>
                            <div 
                            class="<?php echo 'js-house vm-col-12 vm-col-lg-6 vm-col-xl-6'; ?>" 
                            data-category="<?php echo $category; ?>" 
                            data-price="<?php echo trim($price); ?>" 
                            data-rooms="<?php echo trim($rooms); ?>" 
                            data-living-space="<?php echo trim($living_space); ?>"
                            data-area="<?php echo trim($area); ?>"
                            data-address="<?php echo trim($address); ?>"
                            data-municipality="<?php echo trim($municipality); ?>"
                            >
                                <a href="<?php echo get_permalink($item); ?>">
                                    <div class="vm-offerts-list__item">
                                        <div class="vm-offerts-list__item-box">
                                            <?php 
                                            $image_id = get_post_thumbnail_id($item);
                                            $extra_class = '';
                                            $style = '';
                                            if($image_id) {
                                                $image_src = wp_get_attachment_image_src($image_id, 'hero-bg');
                                                $image_width = $image_src[1];
                                                $image_height = $image_src[2];
                                                $image_orientation = ($image_width > $image_height) ? 'landscape' : 'portrait';
                                                $extra_class .= ' ' . $image_orientation;
                                                $image_url_smaller = wp_get_attachment_image_url($image_id, 'large');
                                                // detect if thumbnail is a floor plan or not
                                                $thumbnail_data = get_field('thumbnail_data');
                                                $thumbnail_is_plan = (isset($thumbnail_data[0]['is_plan']) && $thumbnail_data[0]['is_plan'] == true) ? true : false;
                                            }
                                            if($thumbnail_is_plan == true) {
                                                $extra_class .= ' plan';
                                            }
                                            ?>
                                            <div class="vm-offerts-list__item-image">
                                                <img 
                                                data-src="<?php echo $image_url_smaller; ?>" 
                                                class="lazyload <?php echo esc_attr($extra_class); ?>" 
                                                >
                                                <?php if($status == 10 ): ?>
                                                    <span class="vm-badge">
                                                        <span><?php echo __('SÅLD', TEXTDOMAIN); ?></span>
                                                    </span>
                                                <?php elseif($status == 2 ): ?>
                                                    <span class="vm-badge vm-badge--alt">
                                                        <span><?php echo __('KOMMANDE', TEXTDOMAIN); ?></span>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="vm-offerts-list__item-desc">
                                                <div class="vm-grid">
                                                    <div class="vm-col-6">
                                                        <?php if(isset($house_details['price']['startingPrice']) && !empty($house_details['price']['startingPrice'])): ?>
                                                            <?php if($status == 10): ?>
                                                                <h5><?php echo __('Slutpris', TEXTDOMAIN ); ?></h5>
                                                            <?php else: ?>
                                                                <?php if (isset($house_details['price']['text']) && $house_details['price']['text'] == 'accepterat pris') : ?>
                                                                    <h5><?php echo __('Accepterat pris', TEXTDOMAIN); ?></h5>
                                                                <?php else : ?>
                                                                    <h5><?php echo __('Utgångspris', TEXTDOMAIN); ?></h5>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="vm-col-6">
                                                        <?php if(isset($house_details['price']['startingPrice']) && !empty($house_details['price']['startingPrice'])): ?>
                                                            <h5><?php echo vm_number($house_details['price']['startingPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></h5>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="vm-flex">
                                                    <?php if ($property_type == 'houses') : ?>
                                                        <?php if (isset($house_details['baseInformation']['tenure']) && !empty($house_details['baseInformation']['tenure'])) : ?>
                                                            <small><?php echo __('Upplåtelseform', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['tenure']); ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['baseInformation']['tenure']) && !empty($house_details['baseInformation']['tenure'])) : ?>
                                                            <small><?php echo __('Ägandeform', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['tenure']); ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['houseInterior']['numberOfRooms']) && !empty($house_details['houseInterior']['numberOfRooms'])) : ?>
                                                            <small><?php echo __('Rum', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['houseInterior']['numberOfRooms']); ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['houseInterior']['numberOffBedroom']) && !empty($house_details['houseInterior']['numberOffBedroom'])) : ?>
                                                            <small><?php echo __('Varav sovrum', TEXTDOMAIN); ?></small>
                                                            <small><?php 
                                                            $bedroom_text = $house_details['houseInterior']['numberOffBedroom'];
                                                            if (isset($house_details['houseInterior']['maxNumberOffBedroom']) && !empty($house_details['houseInterior']['maxNumberOffBedroom'])) :
                                                                $bedroom_text .= ' - ' . $house_details['houseInterior']['maxNumberOffBedroom'];
                                                            endif;
                                                            echo nl2br($bedroom_text);
                                                            ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])) : ?>
                                                            <small><?php echo __('Boarea', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])) : ?>
                                                            <small><?php echo __('Biarea', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['plot']['area']) && !empty($house_details['plot']['area'])) : ?>
                                                            <small><?php echo __('Tomtarea', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['plot']['area']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])) : ?>
                                                            <small><?php echo __('Byggnadsår', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['building']['buildingYear']) ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['baseInformation']['propertyUnitDesignation']) && !empty($house_details['baseInformation']['propertyUnitDesignation'])) : ?>
                                                            <small><?php echo __('Fastighetsbeteckning', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['propertyUnitDesignation']); ?></small>
                                                        <?php endif; ?>
                                                    <?php elseif ($property_type == 'condominiums') : ?>
                                                        <?php if(isset($house_details['interior']['numberOfRooms']) && !empty($house_details['interior']['numberOfRooms'])): ?>
                                                            <small><?php echo __('Rum', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['interior']['numberOfRooms']); ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])): ?>
                                                            <small><?php echo __('Boarea', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])): ?>
                                                            <small><?php echo __('Biarea', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['monthlyFee']) && !empty($house_details['baseInformation']['monthlyFee'])): 
                                                        $text = '';
                                                        $text .= vm_number($house_details['baseInformation']['monthlyFee']) . ' ' . __('kr', TEXTDOMAIN);
                                                        ?>
                                                            <small><?php echo __('Månadsavgift', TEXTDOMAIN); ?></small>
                                                            <small><?php echo $text; ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['floorAndElevator']['floor']) && $house_details['floorAndElevator']['floor'] !== null): ?>
                                                            <small><?php echo __('Våningsplan', TEXTDOMAIN ); ?></small>
                                                            <?php 
                                                            $floor_text = $house_details['floorAndElevator']['floor'];
                                                            if (isset($house_details['floorAndElevator']['totalNumberOfFloors']) && $house_details['floorAndElevator']['totalNumberOfFloors'] !== null && $house_details['floorAndElevator']['totalNumberOfFloors'] >= $house_details['floorAndElevator']['floor']) :
                                                                $floor_text .= '/' . $house_details['floorAndElevator']['totalNumberOfFloors'];
                                                            endif;
                                                            ?>
                                                            <small><?php echo nl2br($floor_text); ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['floorAndElevator']['elevator']) && !empty($house_details['floorAndElevator']['elevator'])): ?>
                                                            <small><?php echo __('Hiss', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo ($house_details['floorAndElevator']['elevator'] == 'No') ? 'Nej' : 'Ja' ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['balconyPatio']['balcony']) && !empty($house_details['balconyPatio']['balcony'])): ?>
                                                            <small><?php echo __('Balkong', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['balcony']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['balconyPatio']['patio']) && !empty($house_details['balconyPatio']['patio'])) : ?>
                                                            <small><?php echo __('Uteplats', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['patio']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['balconyPatio']['parkingLot']) && !empty($house_details['balconyPatio']['parkingLot'])) : ?>
                                                            <small><?php echo __('Bilplats', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['parkingLot']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])): ?>
                                                            <small><?php echo __('Byggnadsår', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['building']['buildingYear']) ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['baseInformation']['propertyUnitDesignation']) && !empty($house_details['baseInformation']['propertyUnitDesignation'])) : ?>
                                                            <small><?php echo __('Fastighetsbeteckning', TEXTDOMAIN); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['propertyUnitDesignation']); ?></small>
                                                        <?php endif; ?>
                                                    <?php 
                                                    // housingCooperativeses
                                                    else : 
                                                    ?>
                                                        <?php if(isset($house_details['interior']['numberOfRooms']) && !empty($house_details['interior']['numberOfRooms'])): ?>
                                                            <small><?php echo __('Rum', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['interior']['numberOfRooms']); ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])): ?>
                                                            <small><?php echo __('Boarea', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])): ?>
                                                            <small><?php echo __('Biarea', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['baseInformation']['monthlyFee']) && !empty($house_details['baseInformation']['monthlyFee'])): 
                                                        $text = '';
                                                        $text .= vm_number($house_details['baseInformation']['monthlyFee']) . ' ' . __('kr', TEXTDOMAIN);
                                                        ?>
                                                            <small><?php echo __('Månadsavgift', TEXTDOMAIN); ?></small>
                                                            <small><?php echo $text; ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['floorAndElevator']['floor']) && $house_details['floorAndElevator']['floor'] !== null): ?>
                                                            <small><?php echo __('Våningsplan', TEXTDOMAIN ); ?></small>
                                                            <?php 
                                                            $floor_text = $house_details['floorAndElevator']['floor'];
                                                            if (isset($house_details['floorAndElevator']['totalNumberOfFloors']) && $house_details['floorAndElevator']['totalNumberOfFloors'] !== null && $house_details['floorAndElevator']['totalNumberOfFloors'] >= $house_details['floorAndElevator']['floor']) :
                                                                $floor_text .= '/' . $house_details['floorAndElevator']['totalNumberOfFloors'];
                                                            endif;
                                                            ?>
                                                            <small><?php echo nl2br($floor_text); ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['floorAndElevator']['elevator']) && !empty($house_details['floorAndElevator']['elevator'])): ?>
                                                            <small><?php echo __('Hiss', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo ($house_details['floorAndElevator']['elevator'] == 'No') ? 'Nej' : 'Ja' ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['balconyPatio']['balcony']) && !empty($house_details['balconyPatio']['balcony'])): ?>
                                                            <small><?php echo __('Balkong', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['balcony']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['balconyPatio']['patio']) && !empty($house_details['balconyPatio']['patio'])) : ?>
                                                            <small><?php echo __('Uteplats', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['patio']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if (isset($house_details['balconyPatio']['parkingLot']) && !empty($house_details['balconyPatio']['parkingLot'])) : ?>
                                                            <small><?php echo __('Bilplats', TEXTDOMAIN); ?></small>
                                                            <small><?php echo ($house_details['balconyPatio']['parkingLot']) ? 'Ja' : 'Nej'; ?></small>
                                                        <?php endif; ?>
                                                        <?php if(isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])): ?>
                                                            <small><?php echo __('Byggnadsår', TEXTDOMAIN ); ?></small>
                                                            <small><?php echo nl2br($house_details['building']['buildingYear']) ?></small>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="vm-offerts-list__item-title flex items-center">
                                            <h2 class="estate-name"><?php echo get_the_title($item); ?></h2>
                                            <?php if( 
                                                isset($house_details['internetSettings']['bidSetting'])
                                                && $house_details['internetSettings']['bidSetting'] !== 'DontShowBidding' // don't show if bids visibility is set to "DontShowBidding"
                                                && isset($house_details['assignment']['status']['id'])
                                                && $house_details['assignment']['status']['id'] != 10 // don't show bids if house is sold
                                                && isset($house_details['bids']) 
                                                && !empty($house_details['bids']) 
                                            ) { ?>
                                                <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M177.299 130.588c13.458 13.577 35.389 13.633 48.917.125l76.573-76.462c13.54-13.52 13.498-35.473-.093-48.942l-2.034-2.016c-4.443-4.403-11.609-4.39-16.036.03l-109.334 109.15c-4.432 4.424-4.45 11.598-.042 16.046zM254.743 212.751 76.229 391.015c.258.225.519.444.764.689l43.473 43.4c.25.249.474.514.703.776l178.514-178.265zM326.24 73.17l-84.459 84.318 112.533 112.346 84.461-84.319zM381.395 334.409l1.952 1.916c4.453 4.369 11.596 4.334 16.005-.079l109.321-109.408c4.45-4.454 4.433-11.676-.04-16.108l-2.072-2.053c-13.493-13.371-35.256-13.329-48.698.094l-76.685 76.577c-13.587 13.567-13.49 35.613.217 49.061zM55.829 412.897c-.255-.255-.485-.525-.718-.793l-45.804 45.74c-12.41 12.389-12.41 32.476 0 44.865 12.41 12.389 32.53 12.389 44.94 0l45.801-45.737c-.252-.22-.507-.434-.747-.674zM497 482h-23.168l-5.301-38.167a16.569 16.569 0 0 0-16.411-14.289H280.336a16.569 16.569 0 0 0-16.375 14.041L258.033 482h-23.44c-8.284 0-15 6.716-15 15s6.716 15 15 15H497c8.284 0 15-6.716 15-15s-6.715-15-15-15z" fill="#000000" opacity="1" data-original="#000000" class=""></path></g></svg>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php 
                        endforeach;
                    }
                    ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="vm-grid">
        <div class="vm-col-12 vm-col-md-12">
            <?php if(!empty($fields['button'])): ?>
                <a href="<?php echo $fields['button']['url']; ?>" target="<?php echo $fields['button']['target']; ?>" class="vm-button vm-button--dark">
                    <?php echo $fields['button']['title']; ?>
                </a>
            <?php endif; ?>
        </div>
    </div>
</section>
