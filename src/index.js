import "../css/style.scss";
import noUiSlider from 'nouislider';
// import Swiper bundle with all modules installed
import Swiper from 'swiper/bundle';
import GLightbox from 'glightbox';
import AOS from 'aos';
// import autoComplete from "@tarekraafat/autocomplete.js";
import Tagify from '@yaireo/tagify'
import Shuffle from 'shufflejs';
import 'lazysizes';

// import styles bundle
import 'swiper/css/bundle';
const slider_houses = new Swiper('.js-house-slider', {
    loop: true,
    speed: 200,
    pagination: {
        el: '.swiper-pagination',
        clickable: true
    },
    navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
    },
});

function initializeMenuToggle() {
    const hamburger = document.querySelector('.js-mobile-menu');
    const menu = document.querySelector('.js-menu');
    const hamburgerClose = document.querySelector('.js-menu-close');

    const toggleMenu = () => {
        if (menu.classList.contains('active')) {
            menu.classList.remove('active'); // Remove active class
        } else {
            menu.classList.add('active'); // Add active class
        }
    };

    // Event listener for opening and closing the menu
    hamburger.addEventListener('click', toggleMenu);
    hamburgerClose.addEventListener('click', toggleMenu);

    // Optional: Hide the menu after the fade-out animation
    menu.addEventListener('animationend', (e) => {
        if (e.animationName === 'fadeOut') {
            menu.style.display = 'none';
        }
    });
}

// ES7 will have Array.prototype.includes.
function arrayIncludes(array, value) {
    return array.indexOf(value) !== -1;
}

// Write a function that is similar to one above, but doesn't check for exact match, but for partial match.
function isPartialMatch(array, value) {
    return array.some(function(item) {
        return value.includes(item);
    });
}

// Convert an array-like object to a real array.
function toArray(thing) {
    return Array.prototype.slice.call(thing);
}

let currentFormData = {};

// Estates list and filters
class EstatesList {

    constructor(element) {
        this.price_range_timeout;
        this.categories = toArray(document.querySelectorAll('.vm-offerts-list__form-buttons input[type="radio"]'));
        this.price_range = document.getElementById('offer_price_input');
        this.rooms_range = document.getElementById('offer_rooms_input');
        this.living_space_range = document.getElementById('offer_area_input');
        this.address_search = document.querySelector('input[name="offer_address"]');
        this.area_search = document.querySelector('input[name="offer_area_name"]');
        this.municipality_search = document.querySelector('input[name="offer_municipality"]');
        this.broker_search = document.querySelector('input[name="offer_broker"]');
        this.broker_search_value = ( this.broker_search ) ? this.broker_search.value : '';

        this.shuffle = new Shuffle(element, {
            itemSelector: '.js-house',
            speed: 0,
            staggerAmount: 0
        });

        this.filters = {
            category: ['till_salu'],
            price: [],
            rooms: [],
            living_space: [],
            address: [],
            area: [],
            municipality: [],
        };

        this._loadMoreData();
        this._bindEventListeners();
        // this.addSorting();
        // this.addSearchFilter();
        this.filter();
    }

    _loadMoreData = function() {
        const params = new URLSearchParams({
            action: 'get_remaining_estates',
            nonce: custom_loadmore_params.nonce,
            broker: this.broker_search_value
        });
        fetch(custom_loadmore_params.ajaxurl, {
            method: 'POST',
            body: params
        })
        .then(response => response.text())
        .then(data => {
            const estates_list = document.querySelector('.js-houses-list');
            let match = data.match(/class="js-house/g);
            let estates_from_response_count = match ? match.length : 0;
            if(estates_from_response_count) {
                if(estates_list) {
                    estates_list.insertAdjacentHTML("beforeend", data);
                }
                var all_items_in_grid = Array.from(estates_list.children);
                let new_items = all_items_in_grid.slice(-estates_from_response_count);
                this.shuffle.add(new_items);
                let disabled_radios = document.querySelectorAll('.vm-offerts-list__form-radio.disabled');
                if(disabled_radios.length > 0) {
                    disabled_radios.forEach(function(radio) {
                        radio.classList.remove('disabled');
                    });
                }
            } else {
                let disabled_radios = document.querySelectorAll('.vm-offerts-list__form-radio.disabled');
                if(disabled_radios.length > 0) {
                    disabled_radios.forEach(function(radio) {
                        radio.classList.remove('disabled');
                    });
                }
            }
        })
        .catch(error => {
            console.error('Error: ', error);
        });
    }

    _bindEventListeners() {
        this._onCategoryChange = this._handleCategoryChange.bind(this);
        this._onPriceChange = this._handlePriceChange.bind(this);
        this._onRoomsChange = this._handleRoomsChange.bind(this);
        this._onLivingSpaceChange = this._handleLivingSpaceChange.bind(this);
        this._onAddressChange = this._handleAddressChange.bind(this);
        this._onAreaChange = this._handleAreaChange.bind(this);
        this._onMunicipalityChange = this._handleMunicipalityChange.bind(this);
        
        this.categories.forEach(function(input) {
            input.addEventListener('change', this._onCategoryChange);
        }, this);
        this.price_range.addEventListener('input', this._onPriceChange);
        this.rooms_range.addEventListener('input', this._onRoomsChange);
        this.living_space_range.addEventListener('input', this._onLivingSpaceChange);
        this.address_search.addEventListener('input', this._onAddressChange);
        this.area_search.addEventListener('input', this._onAreaChange);
        this.municipality_search.addEventListener('input', this._onMunicipalityChange);
    }

    _handleCategoryChange = function() {
        this.filters.category = this._getCurrentCategoryFilters();
        let new_category_images = document.querySelectorAll('.js-house[data-category="' + this._getCurrentCategoryFilters() + '"] .vm-offerts-list__item-image img:not(.lazyload)');
        new_category_images.forEach(function(image) {
            image.classList.add('lazyload');
        });
        window.tagify.removeAllTags();
        this.filters.address = [];
        this.filters.area = [];
        this.filters.municipality = [];
        this.filter();
        createSearchSuggestions(window.tagify);
    };

    _handlePriceChange = function() {
        this.filters.price = this._getCurrentPriceFilters();
        this.filter();
    }

    _handleRoomsChange = function() {
        this.filters.rooms = this._getCurrentRoomsFilters();
        this.filter();
    }

    _handleLivingSpaceChange = function() {
        this.filters.living_space = this._getCurrentLivingSpaceFilters();
        this.filter();
    }

    _handleAddressChange = function() {
        this.filters.address = this._getCurrentAddressFilters();
        this.filter();
    }

    _handleAreaChange = function() {
        this.filters.area = this._getCurrentAreaFilters();
        this.filter();
    }

    _handleMunicipalityChange = function() {
        this.filters.municipality = this._getCurrentMunicipalityFilters();
        this.filter();
    }

    _getCurrentCategoryFilters = function() {
        return this.categories.filter(function(input) {
            return input.checked;
        }).map(function(input) {
            return input.value;
        });
    };

    _getCurrentPriceFilters = function() {
        return JSON.parse(this.price_range.value);
    };

    _getCurrentRoomsFilters = function() {
        return JSON.parse(this.rooms_range.value);
    };

    _getCurrentLivingSpaceFilters = function() {
        return JSON.parse(this.living_space_range.value);
    }

    _getCurrentAddressFilters = function() {
        return JSON.parse(this.address_search.value);
    }

    _getCurrentAreaFilters = function() {
        return JSON.parse(this.area_search.value);
    }

    _getCurrentMunicipalityFilters = function() {
        return JSON.parse(this.municipality_search.value);
    }

    showHideNoResultsMessage = function() {
        const noResultsMessage = document.querySelector('.no-results');
        if(noResultsMessage) {
            if(this.shuffle.visibleItems == 0) {
                noResultsMessage.classList.remove('hidden');
            } else {
                noResultsMessage.classList.add('hidden');
            }
        }
    }

    filter = function () {
        if(this.hasActiveFilters()) {
            this.shuffle.filter(this.itemPassesFilters.bind(this));
        } else {
            this.shuffle.filter(Shuffle.ALL_ITEMS);
        }

        // check if there are any results
        this.showHideNoResultsMessage();
    };

    hasActiveFilters = function() {
        return Object.keys(this.filters).some(function (key) {
            return this.filters[key].length > 0;
        }, this);
    };

    itemPassesFilters = function(element) {
        var categories = this.filters.category;
        var price_range = this.filters.price;
        var rooms_range = this.filters.rooms;
        var address_search = this.filters.address;
        var area_search = this.filters.area; // Vasastan
        var living_space_range = this.filters.living_space;
        var municipality_search = this.filters.municipality;

        var category = element.getAttribute('data-category');
        var price = element.getAttribute('data-price');
        var rooms = element.getAttribute('data-rooms');
        var address = element.getAttribute('data-address');
        var area = element.getAttribute('data-area'); // Vasastan - something
        var living_space = element.getAttribute('data-living-space');
        var municipality = element.getAttribute('data-municipality');

        // if there are active category filters and category value is not in the array.
        if(
            categories.length > 0 
            && !arrayIncludes(categories, category)
        ) {
            return false;
        }

        // if there is active price filter and price value is not within the range.
        if(
            price_range.length > 0 
            && ( price < price_range[0] || price > price_range[1] )
        ) {
            return false;
        }

        // if there is active rooms filter and rooms value is not within the range.
        if(
            rooms_range.length > 0 
            && ( rooms < rooms_range[0] || ( rooms_range[1] < 5 && rooms > rooms_range[1] ) )
        ) {
            return false;
        }

        // if there is active living space filter and living space value is not within the range.
        if(
            living_space_range.length > 0 
            && ( living_space < living_space_range[0] || living_space > living_space_range[1] )
        ) {
            return false;
        }

        // 3 active filter types (address, area or municipality)
        if(
            address_search.length > 0 
            && area_search.length > 0 
            && municipality_search.length > 0 
            && !arrayIncludes(address_search, address) 
            && !isPartialMatch(area_search, area)
            && !isPartialMatch(municipality_search, municipality)
        ) {
            return false;
        }

        // 2 active filter types (address, area or municipality)
        if(
            address_search.length > 0 
            && area_search.length > 0 
            && municipality_search.length === 0
            && !arrayIncludes(address_search, address) 
            && !isPartialMatch(area_search, area)
        ) {
            return false;
        }
        if(
            address_search.length > 0 
            && area_search.length === 0
            && municipality_search.length > 0
            && !isPartialMatch(area_search, area)
            && !isPartialMatch(municipality_search, municipality)
        ) {
            return false;
        }
        if(
            address_search.length === 0
            && area_search.length > 0
            && municipality_search.length > 0
            && !isPartialMatch(area_search, area)
            && !isPartialMatch(municipality_search, municipality)
        ) {
            return false;
        }

        // 1 active filter type (address, area or municipality)
        if(
            address_search.length > 0 
            && area_search.length === 0
            && municipality_search.length === 0
            && !arrayIncludes(address_search, address) 
        ) {
            return false;
        }
        if(
            address_search.length === 0 
            && area_search.length > 0 
            && municipality_search.length === 0
            && !isPartialMatch(area_search, area)
        ) {
            return false;
        }
        if(
            address_search.length === 0
            && area_search.length === 0 
            && municipality_search.length > 0 
            && !isPartialMatch(municipality_search, municipality)
        ) {
            return false;
        }

        // Default case (no active filter or the values are valid)
        return true;

    };

}

function suggestionItemTemplate(tagData) {
    let typeSwe;
    if( tagData.type == 'address' ) {
        typeSwe = 'Adress';
    } else if( tagData.type == 'area' ) {
        typeSwe = 'Område';
    } else if( tagData.type == 'municipality' ) {
        typeSwe = 'Kommun';
    }
    return `
        <div ${this.getAttributes(tagData)}
            class='tagify__dropdown__item ${tagData.class ? tagData.class : ""}'
            tabindex="0"
            role="option">
            <strong>${tagData.value}</strong>
            <span>${typeSwe}</span>
        </div>
    `
}

document.addEventListener('DOMContentLoaded', () => {
    let estates_list = document.querySelector('.js-houses-list');
    if(estates_list) {
        window.estates = new EstatesList(document.querySelector('.js-houses-list'));
    }

    // Search with tags and autocomplete support
    let input = document.querySelector('input[name=offer_search]');
    if( input ) {
        window.tagify = new Tagify(input, {
            tagTextProp: 'value',
            enforceWhitelist: true,
            whitelist: [],
            dropdown: {
                enabled: 1, // always opens dropdown when input gets focus
                classname: 'suggestions-list',
                searchKeys: ['value']
            },
            templates: {
                dropdownItem: suggestionItemTemplate
            },
        });
        createSearchSuggestions(window.tagify);

        // The below code is printed as escaped, so please copy this function from:
        // https://github.com/yairEO/tagify/blob/master/src/parts/helpers.js#L89-L97
        function escapeHTML( s ){
            return typeof s == 'string' ? s
                .replace(/&/g, "&")
                .replace(/</g, "<")
                .replace(/>/g, ">")
                .replace(/"/g, "\"")
                .replace(/`|'/g, "'")
                : s;
        }

        // The below part is only if you want to split the users into groups, when rendering the suggestions list dropdown:
        // (since each user also has a 'team' property)
        tagify.dropdown.createListHTML = sugegstionsList  => {
            const groupsOfHouses = sugegstionsList.reduce((acc, suggestion) => {
                const type = suggestion.type || 'Not Assigned';
                if( !acc[type] )
                    acc[type] = [suggestion]
                else
                    acc[type].push(suggestion)
                return acc
            }, {})

            const getHousesSuggestionsHTML = groupHouses => groupHouses.map((suggestion, idx) => {
                if( typeof suggestion == 'string' || typeof suggestion == 'number' )
                    suggestion = {value:suggestion}
                var value = tagify.dropdown.getMappedValue.call(tagify, suggestion)
                suggestion.value = value && typeof value == 'string' ? escapeHTML(value) : value
                return tagify.settings.templates.dropdownItem.apply(tagify, [suggestion]);
            }).join("")

            // assign the user to a group
            return Object.entries(groupsOfHouses).map(([type, groupHouses]) => {
                let typeSwe;
                if( type == 'address' ) {
                    typeSwe = 'Adresser';
                } else if( type == 'area' ) {
                    typeSwe = 'Område';
                } else if( type == 'municipality' ) {
                    typeSwe = 'Kommun';
                }
                return `<div class="tagify__dropdown__itemsGroup" data-title="${typeSwe}">${getHousesSuggestionsHTML(groupHouses)}</div>`
            }).join("")
        }

        tagify.on('change', e => {
            if( e.detail.value.length ) {
                let array = JSON.parse(e.detail.value);

                let addresses_array = [];
                let areas_array = [];
                let municipality_array = [];

                // Combined search (multiple addresses and areas can be selected)
                array.map(function(item) {
                    if(item.type == 'address') {
                        addresses_array.push(item.value);
                    }
                    if(item.type == 'area') {
                        areas_array.push(item.value);
                    }
                    if(item.type == 'municipality') {
                        municipality_array.push(item.value);
                    }
                });

                currentFormData['offer_address'] = addresses_array;
                currentFormData['offer_area_name'] = areas_array;
                currentFormData['offer_municipality'] = municipality_array;

            } else {
                currentFormData['offer_address'] = [];
                currentFormData['offer_area_name'] = [];
                currentFormData['offer_municipality'] = [];
            }

            // update the hidden inputs
            var event = new Event('input');
            let address_input = document.querySelector('input[name=offer_address]');
            if( address_input && currentFormData['offer_address'] ) {
                address_input.value = JSON.stringify(currentFormData['offer_address']);
                address_input.dispatchEvent(event);
            }
            let area_input = document.querySelector('input[name=offer_area_name]');
            if( area_input && currentFormData['offer_area_name'] ) {
                area_input.value = JSON.stringify(currentFormData['offer_area_name']);
                area_input.dispatchEvent(event);
            }
            let municipality_input = document.querySelector('input[name=offer_municipality]');
            if( municipality_input && currentFormData['offer_municipality'] ) {
                municipality_input.value = JSON.stringify(currentFormData['offer_municipality']);
                municipality_input.dispatchEvent(event);
            }

        });

    }
});

function createSearchSuggestions() {
    let house_objects = document.querySelectorAll('.js-house.shuffle-item--visible');
    let search_suggestions = [];
    house_objects.forEach(function(house_object) {
        let house_address_raw = house_object.getAttribute('data-address');
        let house_area_raw = house_object.getAttribute('data-area');
        let house_municipality_raw = house_object.getAttribute('data-municipality');
        if(house_address_raw) {
            let house_address = {
                value: house_address_raw,
                type: 'address'
            };
            search_suggestions.push(house_address);
        }
        if(house_area_raw) {
            let house_area = {
                value: house_area_raw,
                type: 'area'
            };
            search_suggestions.push(house_area);
        }
        if(house_municipality_raw) {
            let house_municipality = {
                value: house_municipality_raw,
                type: 'municipality'
            };
            search_suggestions.push(house_municipality);
        }
    });
    search_suggestions = search_suggestions.filter((thing, index, self) =>
        index === self.findIndex((t) => (
            t.value === thing.value && t.type === thing.type
        ))
    );
    if(tagify && search_suggestions.length > 0) {
        // console.log(search_suggestions);
        window.tagify.whitelist = search_suggestions;
    }
}

function createSlider(elementId) {
    var element = document.getElementById(elementId);
    var inputElement = document.getElementById(elementId + '_input');

    if(element && inputElement){
        function getUrlParameter(name) {
            name = name.replace(/[[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        noUiSlider.create(element, {
            connect: true,
            start: [parseInt(element.dataset.vmstart), parseInt(element.dataset.vmend)],
            step: parseInt(element.dataset.vmstep),
            range: {
                'min': parseInt(element.dataset.vmstart),
                'max': parseInt(element.dataset.vmend)
            },
        });

        if(getUrlParameter(elementId)){
            element.noUiSlider.set(JSON.parse(getUrlParameter(elementId)));
        }

        element.noUiSlider.on('update', function(values) {
            var outputValues =  [parseInt(values[0]), parseInt(values[1])];
            inputElement.value = JSON.stringify(outputValues);
            var event = new Event('input');
            inputElement.dispatchEvent(event);
        });

        var lowerValue = element.parentElement.querySelector('.slider-value__lower');
        var higherValue = element.parentElement.querySelector('.slider-value__higher');
        element.noUiSlider.on('update', function (values, handle) {
            var value = values[handle];
            if (handle) {
                var suffix_extra = "";
                if(element.dataset.vmsuffix == 'rum' && parseInt(value) == 5 ){
                    suffix_extra = "+";
                }
                higherValue.innerHTML = number_format(parseInt(value), 0, '.', ' ') + suffix_extra + ' ' + element.dataset.vmsuffix;
            } else {
                lowerValue.innerHTML = number_format(parseInt(value), 0, '.', ' ') + ' ' + element.dataset.vmsuffix;
            }
        });
    }
}

createSlider('offer_price');
createSlider('offer_area');
createSlider('offer_rooms');

document.addEventListener('DOMContentLoaded', function() {
    var contentElement = document.querySelector('.js-details');
    if(!contentElement) return;
    var shortDescription = contentElement.querySelector('.short-description');
    if(!shortDescription) return;
    var ellipsis = shortDescription.querySelector('.ellipsis');
    var hiddenDescription = contentElement.querySelector('.hidden-description');
    var buttonElement = document.querySelector('.js-show-details');
    let descriptionVisible = false;
    buttonElement.addEventListener('click', function() {
        if(descriptionVisible == false) {
            hiddenDescription.style.display = 'inline';
            ellipsis.style.display = 'none';
            buttonElement.textContent = buttonElement.dataset.readless;
            descriptionVisible = true;
        } else {
            hiddenDescription.style.display = 'none';
            ellipsis.style.display = 'inline';
            buttonElement.textContent = buttonElement.dataset.readmore;
            descriptionVisible = false;
        }
    });
});

document.addEventListener('DOMContentLoaded', function() {
    var tabLinks = document.querySelectorAll('.js-tablink');
    for (var i = 0; i < tabLinks.length; i++) {
        tabLinks[i].addEventListener('click', function() {
            if (window.matchMedia('(min-width: 992px)').matches) {
                var tabLinkActive = document.querySelector('.js-tablink.active');
                if (tabLinkActive === this) {
                    return;
                }
            }
            var tabId = this.getAttribute('data-tabid');
            if (this.classList.contains('active')) {
                this.classList.remove('active');
                document.querySelector('#' + tabId).classList.remove('active');
                return;
            }
            var allTabLinks = document.querySelectorAll('.js-tablink');
            for(var j = 0; j < allTabLinks.length; j++) {
                allTabLinks[j].classList.remove('active');
            }
            this.classList.add('active');
            var allTabContents = document.querySelectorAll('.tabcontent');
            for(var k = 0; k < allTabContents.length; k++) {
                allTabContents[k].classList.remove('active');
            }
            var tabContent = document.querySelector('#' + tabId);
            tabContent.classList.add('active');
        });
    }

    // Mobile tablink
    var tabLinkMobile = document.querySelector('.js-tablink-menu-mobile');
    if(tabLinkMobile) {
        tabLinkMobile.addEventListener('click', function() {
            // remove active class from all tablinks
            var allTabLinks = document.querySelectorAll('.js-tablink');
            for(var j = 0; j < allTabLinks.length; j++) {
                allTabLinks[j].classList.remove('active');
            }
            var allTabContents = document.querySelectorAll('.tabcontent');
            for(var k = 0; k < allTabContents.length; k++) {
                allTabContents[k].classList.remove('active');
            }
        });
    }

    // trigger tablink click when clicking on menu anchors
    var menuLinks = document.querySelectorAll('.js-tablink-menu');
    for (var i = 0; i < menuLinks.length; i++) {
        menuLinks[i].addEventListener('click', function() {
            var tabId = this.getAttribute('data-tabid');
            var tabTarget = document.querySelector('.js-tablink[data-tabid="' + tabId + '"]');
            if(tabTarget.classList.contains('active')) return;
            document.querySelector('.js-tablink[data-tabid="' + tabId + '"]').click();
            document.querySelector('.tablink-mobile[data-tabid="' + tabId + '"]').classList.add('active');
        });
    }
});

document.querySelector('.js-search-btn').addEventListener('click', function () {
    this.classList.toggle('active');
    document.querySelector('.js-search').classList.toggle('active');
    if(this.classList.contains('active')){
        document.querySelector('#ajaxsearchlite1 .probox .proinput input').focus();
    } else {
        document.querySelector('#ajaxsearchlite1 .probox .proinput input').blur();
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const showMoreBtns = document.querySelectorAll('#show-more-btn');
    const showPlansBtns = document.querySelectorAll('#show-plans-btn');
    const closeBtns = document.querySelectorAll('.close-modal');

    if (showMoreBtns) {
        showMoreBtns.forEach(function (showMoreBtn) {
           showMoreBtn.addEventListener('click', function() {
                const modal = document.querySelector('.js-gallery-modal');
                if(modal) {
                    modal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    
                    // Load images when the modal is opened
                    const images = modal.querySelectorAll('img[data-src]');
                    images.forEach(img => {
                        // img.setAttribute('src', img.getAttribute('data-src'));
                        // img.removeAttribute('data-src'); // Optional: remove the data-src attribute
                        img.classList.add('lazyload');
                    });
                }
            });
        });
    }
    if(showPlansBtns) {
        showPlansBtns.forEach(function (showPlansBtn) {
            showPlansBtn.addEventListener('click', function() {
                const modal = document.querySelector('.js-plans-modal');
                if(modal) {
                    modal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    
                    // Load images when the modal is opened
                    const images = modal.querySelectorAll('img[data-src]');
                    images.forEach(img => {
                        img.setAttribute('src', img.getAttribute('data-src'));
                        img.removeAttribute('data-src'); // Optional: remove the data-src attribute
                    });
                }
            });
        });
    }
    if(closeBtns) {
        closeBtns.forEach(function (closeBtn) {
            closeBtn.addEventListener('click', function() {
                const modal = document.querySelector('.js-gallery-modal');
                if(modal) {
                    modal.classList.remove('active');
                } 
                const plansModal = document.querySelector('.js-plans-modal');
                if(plansModal) {
                    plansModal.classList.remove('active');
                }
                if(modal || plansModal) {
                    document.body.style.overflow = '';
                }
            });
        });
    }

    initializeMenuToggle();

    // Check if screen size is 992px or larger
    // Initialize AOS
    AOS.init({
        disable: function() {
          var maxWidth = 992;
          return window.innerWidth < maxWidth;
        }
      });
});

function number_format(number, decimals, dec_point, thousands_sep) {
    // Set default values if not provided
    decimals = typeof decimals !== 'undefined' ? decimals : 0;
    dec_point = typeof dec_point !== 'undefined' ? dec_point : '.';
    thousands_sep = typeof thousands_sep !== 'undefined' ? thousands_sep : ',';

    // Convert the number to a string
    var number_str = number.toFixed(decimals);

    // Split the string into integer and decimal parts
    var parts = number_str.split('.');
    var integer_part = parts[0];
    var decimal_part = parts.length > 1 ? dec_point + parts[1] : '';

    // Add thousands separator
    integer_part = integer_part.replace(/\B(?=(\d{3})+(?!\d))/g, thousands_sep);

    // Combine the parts and return the formatted number
    return integer_part + decimal_part;
}

// Body class on scroll
function scrollFunction() {
    if (document.body.scrollTop > 0 || document.documentElement.scrollTop > 0) {
        document.body.classList.add('scrolled');
    } else {
        document.body.classList.remove('scrolled');
    }
}
document.addEventListener('DOMContentLoaded', function() {
    scrollFunction();
});
window.onscroll = function() {
    scrollFunction();
};

// Show more/less visits
document.addEventListener('DOMContentLoaded', function() {
    var button = document.querySelector('.vm-single-offerts__visits__more');
    var rows = document.querySelectorAll('.vm-single-offerts__visits__row.hidden');
    if(!button) return;
    
    button.addEventListener('click', function(e) {
        e.preventDefault();

        // Toggle the hidden class on each row
        rows.forEach(function(row) {
            row.classList.toggle('hidden');
        });

        // Switch the text on the button
        if (button.textContent === 'Fler datum') {
            button.textContent = 'Fewer datum';
        } else {
            button.textContent = 'Fler datum';
        }
    });
});

// Show more/less bids
document.addEventListener('DOMContentLoaded', function() {
    var button = document.querySelector('.vm-single-offerts__bids__more');
    var rows = document.querySelectorAll('.vm-single-offerts__bids__row.hidden');
    if(!button) return;
    
    button.addEventListener('click', function(e) {
        e.preventDefault();

        // Toggle the hidden class on each row
        rows.forEach(function(row) {
            row.classList.toggle('hidden');
        });

        // Switch the text on the button
        if (button.textContent === 'Alla bud') {
            button.textContent = 'Visa färre bud';
        } else {
            button.textContent = 'Alla bud';
        }
    });
});

// Prefill the "select-date" field when clicking on a visit row
document.addEventListener('DOMContentLoaded', function() {
    var rows = document.querySelectorAll('.vm-single-offerts__visits__row');
    var selectDateField = document.querySelector('select[name="select-date"]');

    rows.forEach(function(row) {
        row.addEventListener('click', function(e) {
            var id = this.getAttribute('data-timeslot-id');
            selectDateField.value = id;
        });
    });
});

// When clicking on the anchor link from the mobile menu, close the menu
document.addEventListener('DOMContentLoaded', function() {
    var menuLinks = document.querySelectorAll('.js-menu a');
    if(!menuLinks) return;
    
    menuLinks.forEach(function(menuLink) {
        menuLink.addEventListener('click', function() {
            document.querySelector('.js-mobile-menu').click();
        });
    });
});

// Loop through videos and remove controls
document.addEventListener('DOMContentLoaded', function() {
    var videos = document.querySelectorAll('video');
    if(!videos) return;
    videos.forEach(function(video) {
        video.removeAttribute('controls');
    });
});

// Scroll to mobile tab link when clicked
document.addEventListener('DOMContentLoaded', function() {
    var tabLinks = document.querySelectorAll('.tablink-mobile');
    if(!tabLinks) return;
    tabLinks.forEach(function(tabLink) {
        tabLink.addEventListener('click', function() {
            tabLink.scrollIntoView();
        });
    });
});