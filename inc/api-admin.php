<?php

/**
 * Add a setting page to the admin menu
 */
function vitec_add_admin_menu() {
    add_menu_page(
        'Vitec Sync',
        'Vitec Sync',
        'manage_options',
        'vitec_api_sync',
        'vitec_render_admin_page',
        'dashicons-admin-generic',
        30
    );
}
add_action('admin_menu', 'vitec_add_admin_menu');

/**
 * Render the setting page
 */
function vitec_render_admin_page() {
    ?>
    <style>
        .vitec-sync-settings h3 {
            margin-top: 20px;
        }
        .vitec-status .badge {
            padding: 5px 10px;
            border-radius: 5px;
            color: white;
        }
        .vitec-status .green {
            background-color: #4CAF50;
        }
        .vitec-status .orange {
            background-color: #FF9800;
        }
    </style>
    <div class="wrap">
        <h2>Vitec Sync</h2>
        <div class="card vitec-sync-settings">
            <h3>Last run:</h3>
            <p class="vitec-last-run">
                <?php 
                $last_run = get_option('vitec_last_update', 'Never');
                if ($last_run === 'Never') {
                    echo 'Never';
                } else {
                    $last_run_time = strtotime($last_run);
                    date_default_timezone_set('Europe/Stockholm');
                    echo date('j. n. Y - H:i:s', $last_run_time);
                    $now = time();
                    $diff = $now - $last_run_time;
                    $diff = human_time_diff($last_run_time, $now);
                    echo ' (' . $diff . ' ago)';
                }
                ?>
            </p>
            <h3>Sync status:</h3>
            <p class="vitec-status">
                <?php 
                $running = get_option('vitec_update_running', false);
                if ($running) {
                    echo '<span class="badge green">Running...</span>';
                } else {
                    echo '<span class="badge orange">Idle</span>';
                }
                ?>
            </p>
            <p><em>The status is automatically updated every 5 seconds.</em></p>
    </div>
    <div class="card vitec-sync-settings">
            <h3>Sync reset</h3>
            <p>The last run should always be less than 15 minutes ago, though in rare cases, it may be longer. Use this only in an emergency if the sync is stuck at <em>Running</em> for an extended period (more than 1 hour). This should never be triggered if there is no issue with the sync, as it may corrupt the import process if it is still running.</p>
            <form action="<?php echo admin_url('admin-post.php'); ?>" method="post">
                <input type="hidden" name="action" value="vitec_sync_restart">
                <input type="submit" class="button button-primary" value="Reset the synchronization">
            </form>
            <p><em>After the reset, synchronization will be triggered again at a 3-minute interval.</em></p>
        </div>
    </div>
    <script>
        // Equivalent to PHP's human_time_diff function
        function humanTimeDiff(from, to = Date.now() / 1000) {
            const MINUTE_IN_SECONDS = 60;
            const HOUR_IN_SECONDS = 60 * MINUTE_IN_SECONDS;
            const DAY_IN_SECONDS = 24 * HOUR_IN_SECONDS;
            const WEEK_IN_SECONDS = 7 * DAY_IN_SECONDS;
            const MONTH_IN_SECONDS = 30 * DAY_IN_SECONDS; // Approximate
            const YEAR_IN_SECONDS = 365 * DAY_IN_SECONDS; // Approximate

            let diff = Math.abs(to - from);
            let since;

            if (diff < MINUTE_IN_SECONDS) {
                let secs = Math.max(1, Math.round(diff));
                since = `${secs} second${secs > 1 ? 's' : ''}`;
            } else if (diff < HOUR_IN_SECONDS) {
                let mins = Math.max(1, Math.round(diff / MINUTE_IN_SECONDS));
                since = `${mins} minute${mins > 1 ? 's' : ''}`;
            } else if (diff < DAY_IN_SECONDS) {
                let hours = Math.max(1, Math.round(diff / HOUR_IN_SECONDS));
                since = `${hours} hour${hours > 1 ? 's' : ''}`;
            } else if (diff < WEEK_IN_SECONDS) {
                let days = Math.max(1, Math.round(diff / DAY_IN_SECONDS));
                since = `${days} day${days > 1 ? 's' : ''}`;
            } else if (diff < MONTH_IN_SECONDS) {
                let weeks = Math.max(1, Math.round(diff / WEEK_IN_SECONDS));
                since = `${weeks} week${weeks > 1 ? 's' : ''}`;
            } else if (diff < YEAR_IN_SECONDS) {
                let months = Math.max(1, Math.round(diff / MONTH_IN_SECONDS));
                since = `${months} month${months > 1 ? 's' : ''}`;
            } else {
                let years = Math.max(1, Math.round(diff / YEAR_IN_SECONDS));
                since = `${years} year${years > 1 ? 's' : ''}`;
            }

            return since;
        }

        // Convert UTC timestamp from database to Swedish local time and format it correctly
        function convertToSwedishTime(utcTimestamp) {
            let date = new Date(utcTimestamp + ' UTC'); // Ensure it's interpreted as UTC

            // Extract date components
            let day = date.getDate();
            let month = date.getMonth() + 1; // Months are 0-based
            let year = date.getFullYear();
            let hours = date.getHours().toString().padStart(2, '0');
            let minutes = date.getMinutes().toString().padStart(2, '0');
            let seconds = date.getSeconds().toString().padStart(2, '0');

            return `${day}. ${month}. ${year} - ${hours}:${minutes}:${seconds}`;
        }

        // Refresh the status every 5 seconds
        setInterval(function() {
            jQuery.get('<?php echo admin_url('admin-ajax.php'); ?>', {
                action: 'vitec_get_status'
            }, function(response) {
                var status = jQuery('.vitec-status');
                response = JSON.parse(response);
                if (response.running) {
                    status.html('<span class="badge green">Running...</span>');
                } else {
                    status.html('<span class="badge orange">Idle</span>');
                }
                var lastRun = jQuery('.vitec-last-run');
                if (response.last_run === 'Never') {
                    lastRun.html('Never');
                } else {
                    let swedishTimeString = convertToSwedishTime(response.last_run);
                    let lastRunTime = new Date(response.last_run + ' UTC').getTime() / 1000;
                    let now = Date.now() / 1000;
                    let diffString = humanTimeDiff(lastRunTime, now);

                    lastRun.html(swedishTimeString + ' (' + diffString + ' ago)');
                }
            });
        }, 5000);

        // Add a confirmation dialog to the reset button
        jQuery('input[type="submit"]').click(function() {
            return confirm('Are you sure you want to reset the synchronization?');
        });
    </script>
    <?php
}

/**
 * Get the sync status
 */
function vitec_get_status() {
    $running = get_option('vitec_update_running', false);
    $last_run = get_option('vitec_last_update', 'Never');
    echo json_encode(array(
        'running' => $running,
        'last_run' => $last_run
    ));
    wp_die();
}
add_action('wp_ajax_vitec_get_status', 'vitec_get_status');

/**
 * Reset the sync
 */
function vitec_sync_restart() {
    update_option('vitec_update_running', false);
    wp_redirect(admin_url('admin.php?page=vitec_api_sync'));
    exit;
}
add_action('admin_post_vitec_sync_restart', 'vitec_sync_restart');