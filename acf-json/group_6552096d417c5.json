{"key": "group_6552096d417c5", "title": "CPT - Team", "fields": [{"key": "field_666c4c16f3ead", "label": "Vitec ID", "name": "user_id", "aria-label": "", "type": "text", "instructions": "You can get an user ID by clicking on the user's photo in top right corner in Express. Then by copying the ID from the last part of the URL. <br>Eg.: from this URL: <a href=\"https://express.maklare.vitec.net/user/HAN12915BB82143485AAC354C16C2EFBC80\" target=\"_blank\">https://express.maklare.vitec.net/user/HAN12915BB82143485AAC354C16C2EFBC80</a> - the ID is HAN12915BB82143485AAC354C16C2EFBC80", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_65520998fced7", "label": "Telefon", "name": "reg_number", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_655209aafced8", "label": "Email", "name": "email", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_655209b3fced9", "label": "Link", "name": "link", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}, {"key": "field_658bf4e7a09ab", "label": "Description", "name": "description", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "rows": "", "placeholder": "", "new_lines": "wpautop"}, {"key": "field_662b8e871f2aa", "label": "Detail Page Image", "name": "detail_page_image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}], "location": [[{"param": "post_type", "operator": "==", "value": "team"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": ["the_content", "excerpt", "discussion", "comments", "author"], "active": true, "description": "", "show_in_rest": 0, "modified": 1718373733}