{"key": "group_6552028492df3", "title": "Block - Hero", "fields": [{"key": "field_6567b1c6ea3e5", "label": "Smaller height", "name": "smaller", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_658153e6ccc4e", "label": "Media type", "name": "media_type", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Video", "ui_off_text": "Image", "ui": 1}, {"key": "field_6552028490479", "label": "Background image", "name": "background", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_658153e6ccc4e", "operator": "!=", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "id", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}, {"key": "field_658153b9b84c9", "label": "Background video", "name": "background_video", "aria-label": "", "type": "file", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_658153e6ccc4e", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "url", "library": "all", "min_size": "", "max_size": "", "mime_types": ""}, {"key": "field_655202ac9047a", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_655202b39047b", "label": "Description", "name": "description", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_655202ef9047c", "label": "Buttons", "name": "buttons", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_655202fd9047d", "label": "<PERSON><PERSON>", "name": "button", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "parent_repeater": "field_655202ef9047c"}]}], "location": [[{"param": "block", "operator": "==", "value": "acf/vm-hero"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1702974658}