<?php
/**
 * The Template for displaying Archive pages.
 */

get_header();

$blog_page_id = get_option('page_for_posts');
$blog_hero_image = get_the_post_thumbnail_url($blog_page_id, 'full');
?>

<main id="primary" class="site-main">

    <section class="blog-hero" style="background-image: url('<?php echo esc_url($blog_hero_image); ?>');">
        <div class="blog-hero__overlay">
            <h1 class="blog-hero__title"><?php echo get_the_title($blog_page_id); ?></h1>
        </div>
    </section>

    <section class="blog-archive container">
        <?php if (have_posts()) : ?>
            <div class="blog-grid">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class('blog-card'); ?>>
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="blog-card__image">
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium_large', ['loading' => 'lazy']); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        <div class="blog-card__content">

                            <div class="blog-card__categories">
                                <?php
                                $categories = get_the_category();
                                if (!empty($categories)) :
                                    foreach ($categories as $category) :
                                ?>
                                        <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>"
                                            class="blog-card__category cat-<?php echo esc_attr($category->slug); ?>">
                                            <?php echo esc_html($category->name); ?>
                                        </a>
                                <?php
                                    endforeach;
                                endif;
                                ?>
                            </div>


                            <h2 class="blog-card__title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h2>
                            <div class="blog-card__meta">
                                <?php the_time('F j, Y'); ?> • <?php the_author(); ?>
                            </div>
                            <div class="blog-card__excerpt">
                                <?php the_excerpt(); ?>
                            </div>
                            <a class="blog-card__readmore" href="<?php the_permalink(); ?>">Läs mer →</a>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>

            <?php
            the_posts_pagination(array(
                'mid_size' => 2,
                'prev_text' => __('← Föregående', 'textdomain'),
                'next_text' => __('Nästa →', 'textdomain'),
            ));
            ?>

        <?php else : ?>
            <p>Inga blogginlägg ännu.</p>
        <?php endif; ?>
    </section>
</main>

<?php
get_footer();
