<?php
get_header();
$category = get_queried_object();
?>

<main id="primary" class="site-main">

    <?php
    $category = get_queried_object();
    $category_image = get_field('category_image', 'category_' . $category->term_id);

    $hero_background = $category_image ? esc_url($category_image['url']) : 'https://via.placeholder.com/1600x500?text=Kategori';
    ?>

    <section class="blog-hero" style="background-image: url('<?php echo $hero_background; ?>');">
        <div class="blog-hero__overlay">
            <h1 class="blog-hero__title"><?php echo single_cat_title('', false); ?></h1>

            <?php if (category_description()) : ?>
                <p class="blog-hero__description"><?php echo category_description(); ?></p>
            <?php endif; ?>
        </div>
    </section>


    <section class="blog-archive container">
        <?php if (have_posts()) : ?>
            <div class="blog-grid">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class('blog-card'); ?>>
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="blog-card__image">
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium_large', ['loading' => 'lazy']); ?>
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="blog-card__content">
                            <div class="blog-card__categories">
                                <?php
                                $categories = get_the_category();
                                if (!empty($categories)) :
                                    foreach ($categories as $cat) :
                                ?>
                                        <a href="<?php echo esc_url(get_category_link($cat->term_id)); ?>"
                                            class="blog-card__category cat-<?php echo esc_attr($cat->slug); ?>">
                                            <?php echo esc_html($cat->name); ?>
                                        </a>
                                <?php endforeach;
                                endif;
                                ?>
                            </div>

                            <h2 class="blog-card__title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h2>
                            <div class="blog-card__meta">
                                <?php the_time('F j, Y'); ?> • <?php the_author(); ?>
                            </div>
                            <div class="blog-card__excerpt">
                                <?php the_excerpt(); ?>
                            </div>
                            <a class="blog-card__readmore" href="<?php the_permalink(); ?>">Läs mer →</a>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>

            <?php
            the_posts_pagination([
                'mid_size' => 2,
                'prev_text' => __('← Föregående', 'textdomain'),
                'next_text' => __('Nästa →', 'textdomain'),
            ]);
            ?>

        <?php else : ?>
            <p>Inga inlägg i denna kategori ännu.</p>
        <?php endif; ?>
    </section>
</main>

<?php
get_footer();
