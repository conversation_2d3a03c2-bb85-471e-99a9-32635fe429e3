{"name": "bergets-theme", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "wp-scripts start", "build": "wp-scripts build"}, "keywords": ["WordPress", "Theme"], "author": "<PERSON><PERSON><PERSON>", "devDependencies": {"@wordpress/scripts": "^23.7.0", "nouislider": "^15.7.1", "reset-css": "^5.0.1", "swiper": "^8.4.5"}, "dependencies": {"@yaireo/tagify": "^4.17.9", "aos": "^3.0.0-beta.6", "glightbox": "^3.2.0", "lazysizes": "^5.3.2", "normalize.css": "^8.0.1", "shufflejs": "^6.1.0"}}