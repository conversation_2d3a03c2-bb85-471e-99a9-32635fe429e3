.vm-offerts-list{
    padding: 23px 0 35px;
    @media(min-width: $screen-lg) {
        padding: 35px 0 50px;
    }

    .vm-grid:last-child {
        margin-top: 15px;
    }

    &__items{
        a{
            text-decoration: none;
        }
    }
    .row{
        --bs-gutter-x: 20px;
    }

    &__item{
        margin-bottom: 10px;
        @media(min-width: $screen-md) {
            margin-bottom: 30px;
        }
        @media(min-width: $screen-xl) {
            margin-bottom: 47px;
        }
        &-box{
            position: relative;
            &:hover{
                .vm-offerts-list__item-desc{
                    visibility: visible;
                    opacity: 1;
                    transition: all .2s ease-in-out;
                }
            }
        }
        &-image{
            display: flex;
            justify-content: center;
            height: 244px;
            @media(min-width: $screen-md){
                height: 368px;
            }
            img {
                object-fit: cover;
                width: 100%;
                height: 100%;
                &.portrait {
                    object-fit: contain;
                    height: 100%;
                    width: auto;
                }
                &.plan {
                    object-fit: scale-down;
                    height: 100%;
                    width: auto;
                }
            }
        }
        &-desc{
            transition: all .2s ease-in-out;
            visibility: hidden;
            opacity: 0;
            position: absolute;
            inset: 0;
            padding: 17px 18px;
            background-color: rgba(get_color('bg', 4), .87);
            @media(min-width: $screen-md){
                padding: 26px 28px;
            }
            h5{
                color: get_color('font', 1);
                font-size: 20px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                margin-bottom: 5px;
                @media(min-width: $screen-md) {
                    font-size: 22px;
                    margin-bottom: 10px;
                }
                @media(min-width: $screen-lg){
                    font-size: 24px;
                    margin-bottom: 15px;
                }
                @media(min-width: $screen-xl) {
                    font-size: 26px;
                }
            }
            small{
                color: get_color('font', 1);
                display: block;
                font-size: 15px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                line-height: 1.4;
                @media(min-width: $screen-md) {
                    font-size: 17px;
                }
                @media(min-width: $screen-lg) {
                    font-size: 18px;
                }
                @media(min-width: $screen-xl) {
                    font-size: 19px;
                }
            }
        }
        &-title{
            margin-top: 6px;
            gap: 10px;
            @media(min-width: $screen-lg){
                margin-top: 10px;
            }
            h2 {
                color: get_color('font', 5);
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                text-transform: uppercase;
                @media(min-width: $screen-md) {
                    font-size: 18px;
                }
                @media(min-width: $screen-lg){
                    font-size: 19px;
                }
                @media(min-width: $screen-xl) {
                    font-size: 20px;
                }
            }
            svg {
                width: 13px;
                height: auto;
                @media(min-width: $screen-lg){
                    width: 20px;
                }
            }
        }
    }

    &__filters{
        // margin-bottom: 10px;
        .vm-offerts-list__form{
            .vm-grid{
                padding: 0 30px;

                @media(max-width: $screen-lg){
                    padding: 0 15px;
                }
            }
            &-radio{
                .vm-button{
                    position: relative;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    transition: color background-color .3 ease-in-out;
                    @media(max-width: $screen-sm) {
                        padding: 9px 14px;
                        font-size: 14px;
                        white-space: nowrap;
                    }
                    svg {
                        transition: opacity 0.3s;
                        opacity: 0;
                        visibility: hidden;
                        user-select: none;
                        position: absolute;
                        left: 20px;
                        top: 14px;
                        line-height: 14px;
                        animation: rotation 2.5s linear infinite;
                        @media(max-width: $screen-lg) {
                            top: 14px;
                        }
                        @media(max-width: $screen-md) {
                            top: 12px;
                        }
                        @media(max-width: $screen-sm) {
                            left: 12px;
                            top: 10px;
                        }
                    }
                }
                &.disabled {
                    opacity: 0.5;
                    pointer-events: none;

                    .vm-button {
                        padding-left: 46px;
                        @media(max-width: $screen-lg) {
                            padding-left: 42px;
                            padding-left: 42px;
                        }
                        @media(max-width: $screen-sm) {
                            padding-left: 32px;
                        }

                        svg {
                            display: block;
                            opacity: 0.7;
                            visibility: visible;
                            @media(max-width: $screen-lg) {
                                width: 14px;
                                height: 14px;
                            }
                        }
                    }
                }
                &:not(:last-of-type){
                    margin-right: 5px;
                    @media(min-width: $screen-lg){
                        margin-right: 10px;
                    }
                }
            }
            input[type=radio]{
                display: none;
                &:checked{
                    & + .vm-button{
                        background-color: get_color('bg', 5);
                        color: get_color('font', 1);
                    }
                }
            }

            input[type=text]{
                width: 100%;
                max-width: 100%;
                font-size: 20px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                padding: 14px 20px;
                border-radius: 14px;
                border: 1px solid get_color('border', 6);
                @media(min-width: $screen-lg){
                    width: 327px;
                }
                &:active, &:focus{
                    outline: unset;
                }

                background-image: url('../assets/svg/search.svg');
                background-size: 24px;
                background-position: center right 24px;
                background-repeat: no-repeat;

            }

            h4{
                color: get_color('font', 5);
                font-size: 18px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                margin-top: 20px;
                margin-bottom: 10px;
                @media(min-width: $screen-sm){
                    margin-top: 30px;
                    font-size: 20px;
                }
                @media(min-width: $screen-lg){
                    margin-top: 30px;
                    margin-bottom: 10px;
                    font-size: 22px;
                }
            }
            &-slider{
                margin-bottom: 0;
                @media(min-width: $screen-lg){
                    margin-bottom: 30px;
                }
            }

            &-buttons{
                order: 2;
                @media(min-width: $screen-lg){
                    order: 1
                }
                @media(max-width: $screen-sm){
                    margin-left: -15px;
                    margin-right: -15px;
                    max-width: calc(100% + 30px);
                    padding-left: 15px;
                    gap: 2px;
                    display: flex;
                    overflow-x: auto;

                    /* Hide scrollbar for Chrome, Safari and Opera */
                    &::-webkit-scrollbar {
                        display: none;
                    }

                    /* Hide scrollbar for IE, Edge and Firefox */
                    -ms-overflow-style: none;  /* IE and Edge */
                    scrollbar-width: none;  /* Firefox */
                    
                    label {
                        display: block;
                    }
                }
            }
            &-search{
                order: 1;
                @media(min-width: $screen-lg){
                    order: 2
                }
            }

        }
    }
    h3{
        font-size: 20px;
        @media(min-width: $screen-lg){
            font-size: 30px;
        }
    }

    .loader-wrapper {
        visibility: hidden;
        position: fixed;
        background-color: rgba(#000, 0.5);
        inset: 0;
        z-index: 99999999999999;
        justify-content: center;
        align-items: center;
        opacity: 0;
        display: flex;

        /* Dodane płynne przejścia */
        transition: all 0.3s ease-in-out;

        &.active {
            visibility: visible;
            opacity: 1; /* Dla aktywnego stanu */
        }
    }
    .loader {
        width: 48px;
        height: 48px;
        border: 5px solid get_color('font', 1);
        border-bottom-color: transparent;
        border-radius: 50%;
        display: inline-block;
        box-sizing: border-box;
        animation: rotation 1s linear infinite;
    }

    @keyframes rotation {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

}

.fade-in {
    animation: fadeInAnimation ease 1.2s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
}

@keyframes fadeInAnimation {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

// Badge
.vm-badge{
    font-size: 24px;
    border: 2px solid get_color('border', 5);
    position: absolute;
    top: 22px;
    left: 10px;
    width: 80px;
    height: 40px;
    border-top-left-radius: 80px;
    border-top-right-radius: 80px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    font-size: 14px;
    padding: 12px;
    text-align: center;
    background-color: #141e16;
    color: #fff;

    &--alt {
        background-color: get_color('bg', 3)!important;
        border: 2px solid get_color('border', 3)!important;
        padding: 12px 9px!important;
        span {
            font-size: 10px;
            position: relative;
            top: 3px;
        }
    }
}

// Shuffle positioning
.js-houses-list {
    position: relative;
    overflow: hidden;
}

.js-house {
    width: 50%;
    padding: 10px;
    @media(max-width: $screen-md) {
        width: 100%;
        padding: 5px 0;
    }
}

.no-results {
    display: flex;
    justify-content: center;
}

.hidden {
    display: none;
}

// Lazyload
.vm-offerts-list__item-image .lazyload,
.vm-offerts-list__item-image .lazyloading {
    background-color: #f5f5f5;
    transition: background-color 400ms;
}

.vm-offerts-list__item-image .lazyloaded {
    background-color: none;
}