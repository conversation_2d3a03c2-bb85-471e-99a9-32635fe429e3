.vm-team{
    padding: 67px 0 80px 0;
    @media(max-width: $screen-lg){
        padding: 55px 0 60px 0;
    }
    &__heading{
        color: get_color('font', 5);
        font-size: 36px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -2.5px;
        text-transform: uppercase;
        margin-bottom: 20px;
        @media(max-width: $screen-sm) {
            font-size: 38px;
        }
        @media(min-width: $screen-md) {
           font-size: 42px;
        }
        @media(min-width: $screen-lg) {
           font-size: 46px;
        }
        @media(min-width: $screen-xl) {
            font-size: 50px;
            margin-bottom: 25px;
        }
    }
    &__item{
        position: relative;
        margin-bottom: 14px;
        &-image{
            display: block;
            height: auto;
            background-color: gray;
            margin-bottom: 8px;
            aspect-ratio: 397/489;
            background-size: cover;
            background-position: center;
            border-radius: 20px;
            &.link {
                position: relative;
                &::before {
                    content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="48" height="48" x="0" y="0" viewBox="0 0 24 24" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><g data-name="Layer 2"><path d="M12 18.53a11.71 11.71 0 0 1-7.44-2.65l-3.09-2.53a1.74 1.74 0 0 1 0-2.7l3.09-2.53a11.78 11.78 0 0 1 14.88 0l3.09 2.53a1.74 1.74 0 0 1 0 2.7l-3.09 2.53A11.69 11.69 0 0 1 12 18.53zM12 7a10.22 10.22 0 0 0-6.49 2.28l-3.09 2.53a.25.25 0 0 0 0 .38l3.09 2.53a10.27 10.27 0 0 0 13 0l3.09-2.53a.25.25 0 0 0 0-.38l-3.11-2.53A10.24 10.24 0 0 0 12 7z" fill="%23fff" opacity="1" data-original="%23fff" class=""></path><path d="M12 18.25A6.25 6.25 0 1 1 18.25 12 6.25 6.25 0 0 1 12 18.25zm0-11A4.75 4.75 0 1 0 16.75 12 4.75 4.75 0 0 0 12 7.25z" fill="%23fff" opacity="1" data-original="%23fff" class=""></path><path d="M15 12a3 3 0 1 1-2.2-2.89 1.47 1.47 0 0 0-.3.89 1.5 1.5 0 0 0 1.5 1.5 1.47 1.47 0 0 0 .89-.3 3 3 0 0 1 .11.8z" fill="%23fff" opacity="1" data-original="%23fff" class=""></path></g></g></svg>');
                    font-size: 16px;
                    color: #fff;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    background-color: rgba(20, 30, 22, 0.5);
                    z-index: 1;
                    visibility: hidden;
                    opacity: 0;
                    border-radius: 20px;
                    transition: all 0.2s ease-in-out;
                }
                &:hover {
                    &::before {
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }
        }
        &-meta{
            small{
                color: get_color('font', 5);
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                display: block;
                @media(min-width: $screen-xs) {
                    font-size: 15px;
                }
                @media(min-width: $screen-sm) {
                    font-size: 16px;
                }
                @media(min-width: $screen-md) {
                    font-size: 17px;
                }
                @media(min-width: $screen-lg){
                    font-size: 18px;
                }
                @media(min-width: $screen-xl) {
                    font-size: 19px;
                }
                a{
                    text-decoration: none;
                    color: get_color('font', 5);
                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }
    &__link{
        margin-top: 20px;
        display: inline-block;
        @media(max-width: $screen-lg) {
            margin-top: 15px;
        }
        @media(max-width: $screen-sm) {
            margin-top: 10px;
        }
        text-decoration: underline !important;
    }
    &__logo{
        height: 25px;
        width: 100px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        background-image: url('../assets/svg/hittamaklare.svg');
        position: absolute;
        right: 0;
        bottom: 0;
    }
}