.vm-hero{
    display: flex;
    position: relative;
    overflow: hidden;
    z-index: 1;
    &::before{
        content: '';
        position: absolute;
        inset: 0;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
        background-image: linear-gradient(to top, rgba(0, 0, 0, 0) 75%, rgba(0, 0, 0, 0.25) 100%);
    }
    & > .vm-grid {
        margin-top: auto;
        @media(max-width: $screen-sm) {
            width: 100%;
        }
    }
    &__wrapper{
        display: flex;
        flex-direction: column;
        // min-height: calc(100vh - 98px);
        justify-content: flex-end;
        background-position: center;
        background-size: cover;
        padding: 100px 0;
    }
    &__heading{
        color: get_color('font', 1);
        font-size: 36px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -2.5px;
        text-transform: uppercase;
        margin-bottom: 13px;
        // width: 250px;

        @media(min-width: $screen-sm) {
            // width: 400px;
            max-width: 100%;
            font-size: 48px;
        }
        @media(min-width: $screen-md) {
            font-size: 52px;
        }
        @media(min-width: $screen-lg) {
            width: unset;
            margin-bottom: 10px;
            // font-size: 100px;
            // letter-spacing: -5px;
            font-size: 58px;
        }
        @media(min-width: $screen-xl) {
            font-size: 62px;
            letter-spacing: -3px;
        }
    }
    &__text {
        color: get_color('font', 1);
        font-size: 17px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        width: 500px;
        margin-bottom: 25px;
        
        @media(max-width: $screen-sm) {
            display: none;
            max-width: 100%;
        }
        @media(min-width: $screen-md) {
            font-size: 18px;
        }
        @media(min-width: $screen-lg) {
            margin-bottom: 35px;
            max-width: 100%;
            width: 567px;
        }
        @media(min-width: $screen-xl) {
            font-size: 19px;
        }
    }
    &__buttons{
        .vm-button{
            &:not(:last-of-type){
                margin-right: 20px;
                margin-bottom: 20px;
                @media(max-width: $screen-sm){
                    margin-right: 10px;
                    margin-bottom: 10px;
                }
            }
        }
    }
    .vm-button {
        @media(max-width: $screen-sm){
            color: get_color('font', 5);
            background-color: get_color('font', 1);
        }
    }
    // &--smaller{
    //     .vm-hero__text {
    //         @media(min-width: $screen-xl) {
    //             font-size: 18px;
    //         }
    //     }
    // }
    video{
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        pointer-events: none;
        user-select: none;
    }
}

// Full height hero
.vm-hero {
    height: 100vh;
    max-height: 1080px;
    @media (min-width: 2000px) {
        height: 100vh;
        max-height: 1200px;
    }
    @media (min-width: 2500px) {
        height: 100vh;
        max-height: 1400px;
    }
    &--smaller {
        height: 70vh;
        max-height: calc(1080px * 0.7);
        @media (min-width: 2000px) {
            height: 70vh;
            max-height: calc(1200px * 0.7);
        }
        @media (min-width: 2500px) {
            height: 70vh;
            max-height: calc(1400px * 0.7);
        }
    }
}
.admin-bar .vm-hero {
    height: calc( 100vh - 32px );
    max-height: 1080px;
    @media (max-width: 782px){
        height: calc( 100vh - 46px );
        max-height: 1080px;
    }
    @media (min-width: 2000px) {
        max-height: 1200px;
    }
    @media (min-width: 2500px) {
        max-height: 1400px;
    }
    &--smaller {
        height: calc( 70vh - 32px );
        max-height: calc(1080px * 0.7);
        @media (max-width: 782px){
            height: calc( 70vh - 46px );
            max-height: calc(1080px * 0.7);
        }
        @media (min-width: 2000px) {
            max-height: calc(1200px * 0.7);
        }
        @media (min-width: 2500px) {
            ax-height: calc(1400px * 0.7);
        }
    }
}