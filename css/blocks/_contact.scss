.vm-contact{
    background-color: get_color('bg', 3);
    padding: 33px 0;
    @media(min-width: $screen-md){
        padding: 33px 0 45px 0;
    }
    &__heading{
        color: get_color('font', 1);
        font-size: 36px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -2.5px;
        text-transform: uppercase;
        margin-bottom: 12px;
        @media(max-width: $screen-sm) {
            font-size: 38px;
        }
        @media(min-width: $screen-md) {
            font-size: 42px;
        }
        @media(min-width: $screen-lg) {
            font-size: 46px;
        }
        @media(min-width: $screen-xl) {
            font-size: 50px;
        }
    }
    &__text{
        color: get_color('font', 1);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 15px;
        @media(min-width: $screen-md) {
            font-size: 17px;
        }
        @media(min-width: $screen-lg){
            margin-bottom: 0;
            max-width: 100%;
            width: 567px;
            padding-right: 20px;
            font-size: 18px;
        }
        @media(min-width: $screen-xl) {
            font-size: 19px;
        }
    }
    &__form{
        .vm-grid {
            @media(max-width: $screen-md){
                row-gap: 10px;
            }
        }
        .vm-grid:nth-child(4),
        .vm-grid:nth-child(5) {
            @media(max-width: $screen-md){
                margin-top: 10px;
            }
        }
        .wpcf7-form-control-wrap {
            display: block;
            margin-bottom: 20px;
            @media(min-width: $screen-md){
                margin-bottom: 25px
            }
            @media(min-width: $screen-lg){
                margin-bottom: 35px
            }
            // &[data-name="select-date"] {
            //     margin-bottom: 0;
            // }
        }
        input:not(input[type=submit]), textarea, select{
            background: unset;
            border: unset;
            border-bottom: 1px solid get_color('border', 1);
            width: 100%;
            color: get_color('font', 1);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            letter-spacing: -1px;
            text-transform: uppercase;
            padding: 7px 0;
            @media(min-width: $screen-md) {
                font-size: 17px;
            }
            @media(min-width: $screen-lg){
                font-size: 18px;
            }
            @media(min-width: $screen-xl) {
                font-size: 19px;
            }
            &::placeholder {
                color: get_color('font', 1);
                opacity: 1;
            }

            &::-ms-input-placeholder {
                color: get_color('font', 1);
            }
            &:focus{
                outline: none;
            }
        }
        textarea{
            font-family: 'Kunst Grotesk';
            height: 100px;
            resize: none;
            @media(max-width: $screen-md){
                height: 160px;
            }
        }
        select {
            text-transform: none;
            letter-spacing: normal;
        }
        label {
            margin-bottom: 0;
            font-family: 'Kunst Grotesk';
            display: block;
            margin-bottom: 10px;
            color: get_color('font', 1);
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            letter-spacing: -1px;
            text-transform: uppercase;

            span span {
                text-transform: none;
                letter-spacing: normal;
            }
        }
        .wpcf7-spinner{
            position: absolute;
            right: 140px;
            top: 17px;
        }
    }
    &.vm-contact--short{
        padding: 30px 0;
        @media(min-width: $screen-lg) {
            padding: 50px 0;
        }
        .vm-contact__form{
            .wpcf7-form-control-wrap{
                @media(min-width: $screen-xl) {
                    margin-bottom: 0;
                }
            }
        }
        .vm-col-short{
            display: flex;
            align-items: center;
        }
        .vm-contact__text{
            width: auto;
            margin-bottom: 0;
            @media(max-width: $screen-lg) {
                margin-bottom: 15px;
            }
            @media(max-width: $screen-md) {
                margin-bottom: 5px;
            }
        }
    }
    &.vm-contact--team{
        padding: 40px 0!important;
        @media(min-width: $screen-md){
            padding: 60px 0!important;
        }
        .vm-contact-team{
            margin-bottom: 14px;
            @media(max-width: $screen-md){
                margin-bottom: 0;
            }
            .vm-col-12 {
                @media(max-width: $screen-md){
                    display: flex;
                    align-items: center;
                    gap: 20px;
                }
            }
            &__image{
                display: block;
                margin: 0 auto;
                max-width: 100%;
                width: 300px;
                background-color: gray;
                aspect-ratio: 397/489;
                background-size: cover;
                background-position: center;
                @media(max-width: $screen-xl){
                    width: 240px;
                }
                @media(max-width: $screen-md){
                    width: 120px;
                    margin: 0;
                }
                &.link {
                    position: relative;
                    &::before {
                        content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="48" height="48" x="0" y="0" viewBox="0 0 24 24" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><g data-name="Layer 2"><path d="M12 18.53a11.71 11.71 0 0 1-7.44-2.65l-3.09-2.53a1.74 1.74 0 0 1 0-2.7l3.09-2.53a11.78 11.78 0 0 1 14.88 0l3.09 2.53a1.74 1.74 0 0 1 0 2.7l-3.09 2.53A11.69 11.69 0 0 1 12 18.53zM12 7a10.22 10.22 0 0 0-6.49 2.28l-3.09 2.53a.25.25 0 0 0 0 .38l3.09 2.53a10.27 10.27 0 0 0 13 0l3.09-2.53a.25.25 0 0 0 0-.38l-3.11-2.53A10.24 10.24 0 0 0 12 7z" fill="%23fff" opacity="1" data-original="%23fff" class=""></path><path d="M12 18.25A6.25 6.25 0 1 1 18.25 12 6.25 6.25 0 0 1 12 18.25zm0-11A4.75 4.75 0 1 0 16.75 12 4.75 4.75 0 0 0 12 7.25z" fill="%23fff" opacity="1" data-original="%23fff" class=""></path><path d="M15 12a3 3 0 1 1-2.2-2.89 1.47 1.47 0 0 0-.3.89 1.5 1.5 0 0 0 1.5 1.5 1.47 1.47 0 0 0 .89-.3 3 3 0 0 1 .11.8z" fill="%23fff" opacity="1" data-original="%23fff" class=""></path></g></g></svg>');
                        font-size: 16px;
                        color: #fff;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        background-color: rgba(20, 30, 22, 0.5);
                        z-index: 1;
                        visibility: hidden;
                        opacity: 0;
                        border-radius: 20px;
                        transition: all 0.2s ease-in-out;
                    }
                    &:hover {
                        &::before {
                            visibility: visible;
                            opacity: 1;
                        }
                    }
                }
            }
            &__meta{
                @media(max-width: $screen-md){
                    margin-top: 10px;
                    text-align: left;
                }
                small{
                    color: get_color('font', 6);
                    font-size: 15px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    display: block;
                    @media(min-width: $screen-sm) {
                        font-size: 16px;
                    }
                    @media(min-width: $screen-md) {
                        font-size: 17px;
                    }
                    @media(min-width: $screen-lg){
                        font-size: 18px;
                    }
                    @media(min-width: $screen-xl) {
                        font-size: 19px;
                    }
                    a{
                        text-decoration: underline;
                        color: get_color('font', 6);
                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
            }
            &__double {
                display: flex; 
                justify-content: center; 
                gap: 20px;
                @media(max-width: $screen-md) {
                    gap: 10px;
                }
                .vm-contact-team__image {
                    max-width: 100%;
                }
                .vm-contact-team__meta {
                    @media(max-width: $screen-lg){
                        margin-top: 10px;
                        text-align: left;
                    }
                }
                .vm-col {
                    max-width: 40%;
                    @media(max-width: $screen-md) {
                        max-width: calc(50% - 5px);
                    }
                }
            }
        }
    }
}