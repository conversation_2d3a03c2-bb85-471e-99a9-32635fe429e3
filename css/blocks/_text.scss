.vm-text{
    background-color: get_color('bg', 3);
    padding: 33px 0 22px 0;
    @media(min-width: $screen-md){
        padding: 33px 0 56px 0;
    }
    &__title{
        color: get_color('font', 1);
        font-size: 36px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -2.5px;
        text-transform: uppercase;
        margin-bottom: 12px;
        @media(max-width: $screen-sm) {
            font-size: 38px;
        }
        @media(min-width: $screen-md) {
            font-size: 42px;
        }
        @media(min-width: $screen-lg) {
            font-size: 46px;
        }
        @media(min-width: $screen-xl) {
            font-size: 50px;
        }
    }
    &__content{
        color: get_color('font', 1);
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -0.75px;
        width: 471px;
        max-width: 100%;
        font-size: 16px;
        letter-spacing: normal;
        @media(min-width: $screen-md) {
            font-size: 17px;
        }
        @media(min-width: $screen-xl) {
            font-size: 18px;
        }
        p:not(:last-of-type){
            margin-bottom: 20px;
        }
    }
    .vm-button{
        margin-top: 20px;
        @media(min-width: $screen-md) {
            margin-top: 25px;
        }
        @media(min-width: $screen-xl) {
            margin-top: 30px;
        }
    }
    &__video{
        // margin-top: 131px;
        // @media(min-width: $screen-lg){
        //     margin-top: 72px;
        // }
        display: flex;
        justify-content: center;
        margin-top: -10%;
        @media(max-width: $screen-xl) {
            margin-top: -6%;
        }
        @media(max-width: $screen-lg) {
            margin-top: -4%;
        }
        @media(max-width: $screen-md) {
            margin-top: 0;
        }
        video{
            clip-path: ellipse(50% 89% at 50% 100%);
            width: auto;
            max-width: 100%;
            max-height: 50vh;
            pointer-events: none;
            user-select: none;
            @media(min-width: $screen-xl) {
                max-height: 55vh;
            }
        }
    }
}
.home #bergets-ro-fastighetsformedling {
    padding-bottom: 33px;
    @media(min-width: $screen-md){
        padding-bottom: 45px;
    }
}