@function get_color($type: main, $nth: 1) {
    @if map-has-key($base_colors, $type) {
        @if $nth <=length(map-get($base_colors, $type)) {
        @return nth(map-get($base_colors, $type), $nth);
        }
        @else {
        @return nth(map-get($base_colors, $type), 1);
        }
    }
    @else if map-has-key($use_colors, $type) {
        @if $nth <=length(map-get($use_colors, $type)) {
        @return nth(map-get($use_colors, $type), $nth);
        }
        @else {
        @return nth(map-get($use_colors, $type), 1);
        }
    }
    @else {
    @return nth(map-get($base_colors, "main"), 1);
    }
}

$base_colors: (
    "main": (
        #FFFFFF, //1
        #FFB43F, //2
        #9B3E31, //3
        #141E16, //4
        #000000,  //5
        #B4B4B4  //6
    )
) !default;

$base_colors: map-merge(
    $base_colors,
    (
        "func": (
            #fff
        )
    )
);

$use_colors: (
    "font": (
        get_color(main, 1),  //1
        get_color(main, 2),  //2
        get_color(main, 3),  //3
        get_color(main, 4),  //4
        get_color(main, 5),  //5
    ),
    "bg": (
        get_color(main, 1),  //1
        get_color(main, 2),  //2
        get_color(main, 3),  //3
        get_color(main, 4),  //4
        get_color(main, 5),  //5
    ),
    "border": (
        get_color(main, 1),  //1
        get_color(main, 2),  //2
        get_color(main, 3),  //3
        get_color(main, 4),  //4
        get_color(main, 5),  //5
        get_color(main, 6),  //6
    )
);

$screen-xs: 480px;
$screen-sm: 576px;
$screen-md: 768px;
$screen-lg: 992px;
$screen-xl: 1199px;
$screen-xxl: 1550px;