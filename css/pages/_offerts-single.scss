.vm-single-offerts{

    &__short-desc{
        font-size: 20px;
        max-width: 100%;
        width: 500px;
        line-height: normal;
        @media(max-width: $screen-xl){
            width: 100%;
        }

        .content {
            font-size: 16px;
            // height: 75px;
            margin-bottom: 20px;
            // overflow: hidden;
            @media(min-width: $screen-md) {
                font-size: 17px;
            }
            @media(min-width: $screen-lg) {
                font-size: 18px;
            }
            @media(min-width: $screen-xl) {
                font-size: 19px;
            }
        }
        .small {
            font-size: 16px;
            @media(min-width: $screen-md) {
                font-size: 17px;
            }
            @media(min-width: $screen-lg) {
                font-size: 18px;
            }
            @media(min-width: $screen-xl) {
                font-size: 19px;
            }
        }
    }
    &__table{
        margin-bottom: 40px;

        &__tabs {
            @media(max-width: $screen-lg){
                order: 2;
            }
        }

        &__viewings {
            @media(max-width: $screen-lg){
                order: 1;
            }
        }

        .vm-grid:first-child {
            @media(max-width: $screen-lg){
                display: none;
            }
        }
    }

    .posrel{
        position: relative;
    }

    .gallery-buttons{
        position: absolute;
        bottom: 50px;
        z-index: 99999999999;
        right: 50px;
        @media(max-width: $screen-md){
            right: auto;
            left: auto;
            padding: 0 15px;
        }
    }

    .vm-slider{
        overflow: hidden;
        position: relative;
        user-select: none;
        &::before{
            content: '';
            position: absolute;
            inset: 0;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2;
            background-image: linear-gradient(to top, rgba(0, 0, 0, 0) 75%, rgba(0, 0, 0, 0.4) 100%);
        }
        .swiper-slide {
            display: flex;
            justify-content: center;
        }
        &__item-image{
            // height: 429px;
            object-fit: cover;
            width: 100%;
            height: auto;
            // background-size: cover;
            // background-position: center;
            // background-repeat: no-repeat;
            // @media(min-width: $screen-lg){
            //     height: 769px;
            // }
            &.portrait {
                object-fit: contain;
                height: 100%;
                width: auto;
            }
            &.plan {
                object-fit: scale-down;
                height: 100%;
                width: auto;
            }
        }
        .swiper-button-prev{
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            z-index: 9999;
            cursor: pointer;
            background-color: rgba(255, 255, 255, 0.6);
            width: 46px;
            height: 46px;
            transition: all 0.3s ease-in-out;
        }
        .swiper-button-next{
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            z-index: 9999;
            cursor: pointer;
            background-color: rgba(255, 255, 255, 0.6);
            width: 46px;
            height: 46px;
            transition: all 0.3s ease-in-out;
        }
        .swiper-pagination{
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 999;
            @media (max-width: $screen-md) {
                display: none;
            }
            .swiper-pagination-bullet{
                cursor: pointer;
                display: inline-block;
                height: 12px;
                border-radius: 50%;
                width: 12px;
                background-color: rgba(get_color('bg', 6), 0.35 );
                // border: 2px solid get_color('border', 6);
                &:not(:last-of-type){
                    margin-right: 7px;
                }
                &.swiper-pagination-bullet-active{
                    background-color: get_color('bg', 6);
                }
            }

        }
    }
    &__content{
        padding: 38px 0 38px;
        @media(min-width: $screen-lg){
            padding: 38px 0 45px;
        }
        h2 {
            text-transform: uppercase;
            font-size: 20px;
            @media(min-width: $screen-md) {
                font-size: 22px;
                margin-bottom: 24px;
            }
            @media(min-width: $screen-lg) {
                font-size: 26px;
            }
            @media(min-width: $screen-xl) {
                font-size: 28px;
            }
        }
    }
    &__title {
        gap: 20px;
        margin-bottom: 16px;
        @media(min-width: $screen-md){
            font-size: 30px;
            margin-bottom: 24px;
        }
        svg {
            width: 19.869px;
            height: auto;
            @media(min-width: $screen-md){
                width: 30px;
            }
        }
        .vm-badge {
            position: static;
        }
    }
    h2{
        color: get_color('font', 5);
        font-size: 19.869px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 0!important;
        @media(min-width: $screen-md){
            font-size: 30px;
            margin-bottom: 0!important;
        }
    }
    &__desc{
        @media (max-width: $screen-xl) {
            width: 600px;
            max-width: 100%;
        }
        h5{
            color: get_color('font', 5);
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            margin-bottom: 16px;
            @media(min-width: $screen-md){
                font-size: 22px;
                margin-bottom: 24px;
            }
            @media(min-width: $screen-lg){
                font-size: 26px;
            }
            @media(min-width: $screen-xl) {
                font-size: 28px;
            }
        }
        small{
            color: get_color('font', 5);
            display: block;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 1.4;
            @media(min-width: $screen-md){
                font-size: 20px;
            }
            @media(min-width: $screen-md) {
                font-size: 17px;
            }
            @media(min-width: $screen-lg){
                font-size: 18px;
            }
            @media(min-width: $screen-xl){
                font-size: 19px;
            }
        }
    }
    .vm-tabs{
        margin-top: 20px;
        @media(min-width: $screen-lg){
            margin-top: 25px;
        }
        .tablink {

            display: none;
            @media(min-width: $screen-lg){
                display: inline-block;
            }

            &.active{
                background-color: get_color('bg', 3);
                border-color: get_color('border', 3);
                color: get_color('font', 1);
            }
        }

        .tablink-mobile{
            display: block;
            @media(min-width: $screen-lg){
                display: none;
            }
            background: unset;
            border: unset;
            width: 100%;
            border-top: 1px solid #E9E9E9;
            text-align: left;
            color: get_color('font', 5);
            padding: 16px 0;
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            text-transform: uppercase;
            position: relative;
            &:after{
                font-weight: 300;
                font-size: 30px;
                position: absolute;
                content: "";
                background-image: url('../assets/svg/plus.svg');
                height: 20px;
                width: 20px;
                right: 0;
            }
            &.active{
                &:after{
                    background-image: url('../assets/svg/minus.svg');
                }
            }
        }


        .tabcontent {
            padding-top: 40px;
            display: none;
            &.active{
                display: block;
            }
            h3{
                color: get_color('font', 5);
                font-size: 18px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                margin-bottom: 15px;
                @media(min-width: $screen-md){
                    font-size: 19px;
                    margin-bottom: 20px;
                }
                @media(min-width: $screen-lg){
                    font-size: 20px;
                }
                @media(min-width: $screen-xl) {
                    font-size: 22px;
                }
            }
            table{
                margin-bottom: 40px;
                width: 100%;
                display: block;
                tr{
                    display: flex;
                    width: 100%;
                    max-width: 100%;
                    // display: flex;
                    border-top: 1px solid #E9E9E9;
                    // border-bottom: 1px solid #E9E9E9;
                    @media(max-width: 419px) {
                        display: block;
                        padding: 6px 0 4px;
                    }
                    td{
                        &:nth-of-type(1){
                            width: 160px;
                            padding-right: 10px;
                            @media(min-width: $screen-lg){
                                width: 340px;
                            }
                            @media(min-width: $screen-xs){
                                width: 200px;
                            }
                            @media(max-width: 419px) {
                                width: 100%;
                                padding-right: 0;
                            }
                        }
                        width: calc(100% - 160px);
                        flex-shrink: 0;
                        padding: 6px 0;
                        color: get_color('font', 5);
                        font-size: 16px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: normal;
                        overflow-wrap: break-word;
                        word-wrap: break-word;
                        @media(max-width: 419px) {
                            display: block;
                            padding: 0 0 2px;
                            width: 100%;
                        }
                        @media(min-width: $screen-xs) {
                            width: calc(100% - 200px);
                        }
                        @media(min-width: $screen-md) {
                            font-size: 17px;
                            padding: 8px 0;
                        }
                        @media(min-width: $screen-lg){
                            font-size: 18px;
                            padding: 10px 0;
                            width: calc(100% - 340px);
                        }
                        @media(min-width: $screen-xl) {
                            padding: 12px 0;
                        }
                        a {
                            display: flex;
                            width: 100%;
                        }
                        
                        strong{
                            font-weight: 700;
                        }
                    }
                }
                tbody {
                    display: block;
                }
            }
            .content-text{
                font-size: 16px;
                @media(min-width: $screen-md) {
                    font-size: 17px;
                }
                @media(min-width: $screen-lg){
                    font-size: 18px;
                }
                h1, h2, h3, h4, h5, h6{
                    color: get_color('font', 5);
                    font-size: 30px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    margin-bottom: 10px;
                }
                p, span{
                    color: get_color('font', 5);
                    font-style: normal;
                    font-weight: 500;
                    line-height: normal;
                    font-size: 16px;
                    @media(min-width: $screen-md) {
                        font-size: 17px;
                    }
                    @media(min-width: $screen-lg){
                        font-size: 18px;
                    }
                }
                p{
                    margin-bottom: 10px;
                }
            }
            a{
                img{
                    vertical-align: middle;
                    margin-right: 10px;
                }
                color: get_color('font', 5);
                text-decoration: none;

            }
        }
        #tab6 {
            table {
                tr {
                    td{
                        &:nth-of-type(1){
                            width: 100%;
                        }
                    }
                }
            }
        }
    }

    &__visits,
    &__bids {
        display: flex;
        flex-direction: column;
        h3 {
            order: 1;
            color: get_color('font', 5);
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            margin-top: 50px;
            margin-bottom: 24px;
            @media(max-width: $screen-lg){
                font-size: 20px;
                margin-bottom: 20px;
            }
            @media(max-width: $screen-xl){
                margin-top: 20px;
            }
            @media(min-width: $screen-md){
                font-size: 22px;
            }
            @media(min-width: $screen-lg){
                font-size: 24px;
            }
            @media(min-width: $screen-xl) {
                font-size: 26px;
            }
        }
        &__rows {
            order: 2;
        }
        &__row {
            display: flex;
            border-top: 1px solid #E9E9E9;
            padding: 15px 0 15px 10px;
            text-decoration: none;
            color: #000;
            &:hover {
                background-color: #F5F5F5;
            }
            &.hidden {
                display: none;
            }
            .icon {
                svg {
                    position: relative;
                    top: -1px;
                    width: 18px;
                    height: auto;
                    @media(max-width: $screen-lg){
                        width: 15px;
                    }
                }
            }
            .inner {
                margin-left: 20px;
                @media(max-width: $screen-lg){
                    margin-left: 15px;
                }
                .date {
                    margin-bottom: 5px;
                    font-size: 16px;
                    @media(max-width: $screen-md){
                        font-size: 17px;
                    }
                    @media(min-width: $screen-lg) {
                        font-size: 18px;
                    }
                    @media(min-width: $screen-xl) {
                        font-size: 19px;
                    }
                }
                .desc {
                    font-size: 14px;
                    line-height: 1.2;
                    opacity: .6;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    @media(min-width: $screen-lg){
                        font-size: 15px;
                    }
                    div {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border: 1px solid #b4b4b4;
                        color: #000;
                        width: 20px;
                        height: 20px;
                        font-size: 14px;
                        @media(min-width: $screen-lg){
                            top: -1px;
                        }
                    }
                }
            }
        }
        .links {
            order: 3;
            border-top: 1px solid #E9E9E9;
            // display: flex;
            align-items: center;
            padding-top: 20px;
            // gap: 20px;
            a {
                color: #000;
                font-size: 16px;
            }
            // .btn {
            //     color: #000;
            //     font-size: 15px;
            //     font-style: normal;
            //     font-weight: 400;
            //     line-height: normal;
            //     text-transform: uppercase;
            //     text-decoration: unset;
            //     padding: 6px 12px;
            //     position: relative;
            //     font-family: "Kunst Grotesk";
            //     display: inline-block;
            //     border: 1.317px solid #000;
            //     transition: all 0.3s ease-in-out;
            //     background-color: transparent;
            //     border-radius: 25px;
            //     &:hover {
            //         background-color: #000;
            //         color: #fff;
            //     }
            // }
        }
    }
    &__visits {
        & > :nth-child(2):last-child,
        & > :nth-child(3):last-child {
            margin-bottom: 20px;
            // @media(max-width: $screen-lg){
            //     margin-bottom: 80px;
            // }
        }
    }
    &__bids {
        margin-bottom: 40px;
        @media(max-width: $screen-lg){
            margin-bottom: 0;
        }
        &__row {
            &:hover {
                background-color: #fff;
            }
            .icon {
                svg {
                    top: 1px;
                    width: 18px;
                    @media(max-width: $screen-lg){
                        width: 15px;
                    }
                }
            }
        }
    }

    &.sold {
        .vm-single-offerts__short-desc {
            .content,
            .vm-button {
                opacity: .7;
                pointer-events: none;
            }
        }
        .vm-single-offerts__desc,
        .vm-tabs .tabcontent {
            opacity: .7;
        }

        // Not allow text select on important texts
        .vm-single-offerts__content,
        .vm-single-offerts__table {
            user-select: none;
        }
    }
}

// Full height slider
.single-houses .vm-slider {
    height: 100vh;
    max-height: 1080px;
}
.admin-bar.single-houses .vm-slider {
    height: calc( 100vh - 32px );
    max-height: 1080px;
    @media (max-width: 782px){
        height: calc( 100vh - 46px );
        max-height: 1080px;
    }
}

.vm-grid--gallery {
    margin-bottom: 60px;
    margin-top: 60px;
    @media(min-width: $screen-md){
        margin-bottom: 80px;
        margin-top: 80px;
    }
    .vm-offerts-list__item-image {
        display: block;
        object-fit: cover;
        width: 100%;
    }
    .vm-grid {
        row-gap: 0;
    }
    .vm-gallery-item {
        display: block;
        margin-bottom: 20px;
    }
    .hidden-item {
        opacity: 0;
        max-height: 0;
        visibility: hidden;
        overflow: hidden;
        transform: translateY(20px);
        margin-bottom: 0;
        transition: opacity 0.3s, transform 0.3s, visibility 0s 0.3s, max-height 0.3s, margin-bottom 0.3s;
    }
    
    .show-item {
        opacity: 1;
        max-height: 368px; /* Adjust as per your item's maximum height */
        visibility: visible;
        transform: translateY(0);
        margin-bottom: 20px;
        transition: opacity 0.3s, transform 0.3s, max-height 0.3s, margin-bottom 0.3s;
    }

    .gallery-buttons {
        margin-top: 10px;
    }

    &.vm-grid--plans {
        .vm-offerts-list__item-image {
            display: block;
            object-fit: contain;
            width: 100%;
        }
    }
}

.expandable-section {
    height: 100px; /* Change as needed */
    overflow: hidden;
    transition: height .3s ease-out;
}

.expandable-section.expanded {
    height: auto;
}

.modal {
    display: block;
    position: fixed;
    visibility: hidden;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    z-index: 999999999;
    background-color: get_color('font', 1);
    opacity: 0;
    transition: .4s ease-in-out;
    &.active {
        visibility: visible;
        opacity: 1;
        overscroll-behavior: contain;
    }
    .img-wrapper {
        padding: 0 2rem 2rem;
        img {
            max-height: calc(100vh - 103px);
        }
    }
}

.close-modal {
    font-size: 16px;
    svg {
        margin-left: 0.5rem;
        -webkit-transform: scale(1.5);
        transform: scale(1.5);
    }
}

.modal-header {
    position: fixed;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 3rem 2rem;
    background: get_color('font', 1);
}

.close-modal {
    display: flex;
    position: absolute;
    right: 2rem;
    align-items: center;
    cursor: pointer;
}

.modal .img-wrapper {
    margin-top: 86px;
    @media(max-width: $screen-lg){
        margin-top: 73px;
    }

    figure {
        margin-bottom: 2rem;
    }
}

.landscape {
    text-align: center;
}

.gallery-buttons{
    display: flex;
    gap: 15px;
    @media(max-width: $screen-md){
        width: 100%;
        flex-wrap: wrap;
        justify-content: center;
    }
}

.single-offerts-hero {
    .gallery-buttons button {
        box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    }
}

.js-show-details {
    @media(max-width: $screen-xl){
        margin-bottom: 20px;
    }
}

.vm-single-offerts .vm-tabs .tabcontent {
    width: 980px;
    max-width: 100%;
    padding-right: 20px;
    @media(max-width: $screen-lg){
        padding-right: 0;
    }
}

// Object fit contain
.contain {
    object-fit: contain!important;
}

// Subgrid for table
.vm-subgrid {
    display: grid;
    grid-template-rows: subgrid;
}