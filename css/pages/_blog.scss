.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.blog-hero {
    position: relative;
    width: 100%;
    height: 50vh;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 3rem;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;

    &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.4);
        z-index: 1;
    }


}

.blog-hero__title {
    position: relative;
    z-index: 2;
    font-size: 5rem;
    text-transform: uppercase;
    color: #fff;
    text-align: center;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    max-width: 1100px;
}

.blog-card {
    background: #fff;
    border-radius: 0px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    width: 100%;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
    }
}

.blog-card__image img {
    width: 100%;
    height: auto;
    display: block;
}

.blog-card__categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.blog-card__category {
    display: inline-block;
    font-size: 1.2rem;
    font-weight: 600;
    padding: 0.35rem 0.75rem;
    border-radius: 5px;
    text-transform: uppercase;
    letter-spacing: 0.03em;
    text-decoration: none;
    background-color: #202020;
    color: #ffffff;
    transition: background-color 0.3s ease, color 0.3s ease;
    white-space: nowrap;
}

.blog-card__content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.blog-card__title {
    font-size: 2rem;
    margin: 0 0 0.75rem;
    font-weight: 700;
    line-height: 1.3;
    color: #222;
    text-transform: uppercase;

    a {
        text-decoration: none;
        color: inherit;

        &:hover {
            text-decoration: underline;
        }
    }
}

.blog-card__meta {
    font-size: 1.3rem;
    color: #777;
    margin-bottom: 1rem;
}

.blog-card__excerpt {
    font-size: 1.6rem;
    line-height: 1.8rem;
    color: #000;
    margin-bottom: 1.5rem;
}

.blog-card__readmore {
    font-weight: bold;
    color: #000;
    text-decoration: none;
    font-size: 1.4rem;
}

.blog-card__readmore:hover {
    text-decoration: underline;
}

.nav-links {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
    gap: 1rem;

    a, 
    span {
        padding: 0.75rem 1.25rem;
        background-color: #f7f7f7;
        color: #333;
        text-decoration: none;
        border-radius: 8px;
        font-weight: bold;
        transition: background-color 0.3s ease;
    }

    a:hover {
        background-color: #222;
        color: #fff;
    }

    .current {
        background-color: #222;
        color: #fff;
    }
}

.single-post-hero {
    position: relative;
    width: 100%;
    height: 70vh;
    min-height: 400px;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4rem;
}

.single-post-hero__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
}

.single-post-hero__content {
    position: relative;
    z-index: 2;
    max-width: 800px;
}

.single-post-hero__title {
    font-size: 5rem;
    color: #fff;
    margin-bottom: 1rem;
    line-height: 1.2;
    text-transform: uppercase;
}

.single-post-hero__excerpt {
    font-size: 2rem;
    color: #eee;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.single-post-hero__cta {
    display: inline-block;
    padding: 1rem 2rem;
    background-color: #fff;
    color: #333;
    font-weight: bold;
    border-radius: 50px;
    text-decoration: none;
    transition: background-color 0.3s ease, color 0.3s ease;
    font-size: 2rem;
}

.single-post-hero__cta:hover {
    background-color: #333;
    color: #fff;
}

.single-post-content {
    max-width: 800px;
    margin: 0 auto 4rem;
}

.single-post__text {
    font-size: 1.5rem;
    line-height: 1.8;
    color: #333;

    h2 {
        font-size: 3rem;
        margin-top: 2.5rem;
        margin-bottom: 1rem;
        color: #111;
        font-weight: 700;
        line-height: 1.3;
    }

    h3 {
        font-size: 2rem;
        margin-top: 2rem;
        margin-bottom: 1rem;
        color: #222;
        font-weight: 700;
        line-height: 1.4;
    }

    h4 {
        font-size: 1.5rem;
        margin-top: 1.5rem;
        margin-bottom: 1rem;
        color: #333;
        font-weight: 600;
        line-height: 1.5;
    }

    p {
        margin-bottom: 1.5rem;
    }

    ul,
    ol {
        margin-left: 2rem;
        margin-bottom: 2rem;
    }

    li {
        margin-bottom: 0.75rem;
    }

    strong {
        font-weight: bold;
    }

    em {
        font-style: italic;
    }
}

.single-post-meta {
    margin-top: 1.5rem;
    text-align: center;
    font-size: 1.2rem;
    color: #ffffff;
}

.single-post-meta__content {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.meta-item {
    font-weight: 500;
}

.meta-separator {
    margin: 0 0.5rem;
    color: #ffffff;
}

.related-posts {
    margin-top: 6rem;
    margin-bottom: 4rem;
}

.related-posts__title {
    text-align: center;
    font-size: 3rem;
    margin-bottom: 5rem;
    text-transform: uppercase;
}

@media (max-width: 765px) {
    .single-post-hero {
        height: 500px;
        padding: 4rem 1rem 2rem 1rem;
    }

    .single-post-hero__overlay {
        padding: 2rem 1rem;
    }

    .single-post-hero__title {
        font-size: 3rem;
        line-height: 1.2;
    }

    .single-post-hero__excerpt {
        font-size: 1.5rem;
        margin-top: 1rem;
        line-height: 1.5;
    }

    .single-post-hero__cta {
        padding: 0.75rem 1.5rem;
        font-size: 1.5rem;
    }

    .meta-separator {
        display: none;
    }

    .blog-hero__title {
        font-size: 3rem;
    }
}
