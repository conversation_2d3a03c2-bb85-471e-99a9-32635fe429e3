.single-page-content {
    padding: 60px 0;
    width: 920px;
    position: relative;
    margin: 0 auto;
    @media(max-width: $screen-md) {
        padding: 40px 0;
    }
    p {
        font-size: 17px;
        line-height: 1.5;
        margin-bottom: 20px;
        @media(max-width: $screen-sm) {
            font-size: 16px;
        }
    }
    ul, ol {
        margin-bottom: 20px;
        li {
            font-size: 17px;
            line-height: 1.5;
            margin-bottom: 5px;
            @media(max-width: $screen-sm) {
                font-size: 16px;
            }
        }
    }
    ul {
        list-style: disc;
        padding-left: 20px;
    }
    ol {
        list-style: decimal;
        padding-left: 20px;
    }
    h1, h2, h3, h4, h5, h6 {
        padding-top: 10px;
        line-height: 1.2;
        margin-bottom: 20px;
        font-weight: 700;
    }
    h1 {
        text-transform: uppercase;
        font-size: 28px;
        margin-bottom: 30px;
        @media(max-width: $screen-lg) {
            font-size: 26px;
        }
        @media(max-width: $screen-sm) {
            font-size: 24px;
        }
    }
    h2 {
        font-size: 22px;
        @media(max-width: $screen-lg) {
            font-size: 20px;
        }
        @media(max-width: $screen-sm) {
            font-size: 18px;
        }
    }
    h3 {
        font-size: 21px;
        @media(max-width: $screen-lg) {
            font-size: 19px;
        }
        @media(max-width: $screen-sm) {
            font-size: 17px;
        }
    }
    h4 {
        font-size: 20px;
        @media(max-width: $screen-lg) {
            font-size: 18px;
        }
        @media(max-width: $screen-sm) {
            font-size: 16px;
        }
    }
    h5 {
        font-size: 19px;
        @media(max-width: $screen-lg) {
            font-size: 17px;
        }
        @media(max-width: $screen-sm) {
            font-size: 16px;
        }
    }
    a {
        color: get_color('font', 3);
        text-decoration: underline;
    }
}