@font-face {
    font-family: 'Kunst Grotesk';
    src: url('../assets/fonts/kunstgrotesk/FTKunstGrotesk-Regular.woff2') format('woff2'),
    url('../assets/fonts/kunstgrotesk/FTKunstGrotesk-Regular.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

html {
    font-size: 10px;
    // scroll-behavior: smooth;
}

body {
    font-family: 'Kunst Grotesk';
}

html.dark body {
    background-color: get_color('bg', 3);
}


img {
    max-width: 100%;
    height: auto;
}

* {
    -webkit-box-sizing: border-box; /* Safari/Chrome, other WebKit */
    -moz-box-sizing: border-box;    /* Firefox, other Gecko */
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
}

.text-normal {
    font-size: 18px;
    @media(max-width: $screen-md){
        font-size: 17px;
    }
}

// Remove video controls
video::-webkit-media-controls,
video::-moz-media-controls, 
video::-o-media-controls, 
video::-ms-media-controls {
  display: none;
}