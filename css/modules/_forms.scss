.wpcf7-not-valid-tip,
.wpcf7 form .wpcf7-response-output {
    font-size: 15px;
    color: #fff;
    margin-top: 1em;
    font-style: italic;
    margin-bottom: -1em;
}

.wpcf7 form .wpcf7-response-output {
    margin: 1.5em 0 0;
    padding: 0 10px;
    border: none!important;
    border-left: 3px solid #fff!important;
}

.wpcf7 .vm-col-align-right {
    text-align: left;
}

.vm-contact--normal,
.vm-contact--team {
    .wpcf7 form .wpcf7-response-output {
        margin: 1.5em 0 0;
        padding: 0 10px;
        border: none!important;
        border-left: 3px solid #fff!important;
        @media (min-width: 1300px) {
            position: relative;
            top: -70px;
            width: calc(100% - 280px);
        }
    }

    .wpcf7 .vm-col-align-right {
        text-align: left;
        @media (min-width: 1300px) {
            text-align: right;
        }
    }
}

.vm-contact--short {
    .wpcf7 form .wpcf7-response-output {
        @media (min-width: 1200px) {
            margin-top: 1em!important;
        }
    }
    .vm-contact__form .vm-grid {
        @media (max-width: 1199px) {
            .vm-col-xl-3:nth-child(1) {
                order: 1;
            }
            .vm-col-xl-3:nth-child(2) {
                order: 2;
            }
            .vm-col-xl-3:nth-child(3) {
                order: 3;
            }
            .vm-col-xl-3:nth-child(4) {
                order: 5;
            }
            .vm-col-12:last-child {
                order: 4;
            }
        }
    }
}

// hide recaptcha badge
.grecaptcha-badge {
    visibility: hidden;
}

// acceptance checkbox
.wpcf7 form .wpcf7-acceptance {
    label {
        display: flex;
        align-items: center;
        text-transform: none;
        letter-spacing: normal;
        font-size: 16px;
        @media(min-width: $screen-xl) {
            font-size: 17px;
        }
        input {
            margin-right: 10px;
            width: 15px;
            height: 15px;
        }
        a {
            color: #fff;
            text-decoration: none;
            &:hover {
                text-decoration: underline;
            }
        }
    }
    .wpcf7-list-item {
        display: block;
        margin-left: 0;
    }
}