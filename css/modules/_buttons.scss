.vm-button{
    color: get_color('font', 1);
    text-align: center;
    font-size: calc(20px * 0.85);
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: uppercase;
    padding: calc(15px * 0.85) calc(32px * 0.85) calc(14px * 0.85);
    border-radius: 20px;
    text-decoration: unset;
    display: inline-block;
    border: 1.317px solid get_color('border', 1);
    transition: all .3s ease-in-out;
    background-color: transparent;
    cursor: pointer;
    max-width: fit-content;
    @media(max-width: $screen-lg) {
        font-size: 16px;
        padding: 11px 26px;
    }
    @media(max-width: $screen-md) {
        font-size: 16px;
        padding: 10px 24px;
    }
    &--light{
        &:hover{
            background-color: get_color('bg', 1);
            transition: all .2s ease-in-out;
            color: get_color('font', 5);
        }
    }
    &--dark{
        color: get_color('font', 5);
        border-color: get_color('border', 5);
        &:hover{
            background-color: get_color('bg', 5);
            transition: all .2s ease-in-out;
            color: get_color('font', 1);
        }
    }
    &--dark-green{
        color: get_color('font', 1);
        border-color: get_color('border', 1);
        &:hover{
            background-color: get_color('bg', 1);
            transition: all .2s ease-in-out;
            color: get_color('font', 4);
        }
    }
    &--bronze{
        &:hover{
            background-color: get_color('bg', 1);
            transition: all .2s ease-in-out;
            color: get_color('font', 3);
        }
    }
    &--white{
        background-color: get_color('bg', 1);
        color: get_color('font', 5);
        border-color: get_color('border', 1);
        &:hover{
            background-color: get_color('bg', 5);
            transition: all .2s ease-in-out;
            color: get_color('font', 1);
            border-color: get_color('border', 5);
        }
    }
    &--mb-3{
        margin-bottom: 30px;
    }
}