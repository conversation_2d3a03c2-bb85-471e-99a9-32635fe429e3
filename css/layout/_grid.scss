$prefix: vm-;

$container-widths: (
    default: 100%,
    sm: 540px,
    md: 720px,
    lg: 100%,
    xl: 1300px,
    xxl: 1722px
);

$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1199px;
$breakpoint-xxl: 1600px;

$external-paddings: (
        default: 20px,
        sm: 15px,
        md: 10px,
        lg: 25px,
        xl: 30px,
        xxl: 30px
);

@mixin grid-breakpoint($cols, $gap, $row-gap, $container-width, $col-width) {
    --grid-col-amount: #{$cols};
    --grid-col-gap: #{$gap};
    --grid-row-gap: #{$row-gap};
    --container-width: #{$container-width};
    --col-width: #{$col-width};
}

@function get-external-padding($breakpoint) {
    @return map-get($external-paddings, $breakpoint);
}

.#{$prefix}grid {
    display: grid;
    position: relative;
    z-index: 1;
    margin: 0 auto;
    overflow-x: visible;
    padding: 0 get-external-padding(default); // Add external padding
    grid-auto-columns: minmax(0, 1fr);
    grid-auto-flow: row;
    max-width: 100%;
    @include grid-breakpoint(12, 20px, 20px, map-get($container-widths, default), auto);
    width: calc(var(--container-width) + 2 * get-external-padding(default) + var(--col-width));

    &--align-center{
        align-items: center;
    }

    &--full{
        width: 100% !important;
        padding: 0 !important;
    }

    @media (min-width: #{$breakpoint-sm}) {
        width: map-get($container-widths, sm);
        @include grid-breakpoint(12, 20px, 20px, map-get($container-widths, sm), auto);
        padding: 0 get-external-padding(sm);
    }

    @media (min-width: #{$breakpoint-md}) {
        width: map-get($container-widths, md);
        @include grid-breakpoint(12, 20px, 20px, map-get($container-widths, md), auto);
        padding: 0 get-external-padding(md);
    }

    @media (min-width: #{$breakpoint-lg}) {
        width: map-get($container-widths, lg);
        @include grid-breakpoint(12, 20px, 20px, map-get($container-widths, lg), auto);
        padding: 0 get-external-padding(lg);
    }

    @media (min-width: #{$breakpoint-xl}) {
        width: map-get($container-widths, xl);
        @include grid-breakpoint(12, 20px, 20px, map-get($container-widths, xl), 20px);
        padding: 0 get-external-padding(xl);
    }

    @media (min-width: #{$breakpoint-xxl}) {
        width: map-get($container-widths, xxl);
        @include grid-breakpoint(12, 20px, 20px, map-get($container-widths, xxl), 20px);
        padding: 0 get-external-padding(xxl);
    }

    column-gap: var(--grid-col-gap);
    row-gap: var(--grid-row-gap);

    grid-template-columns: repeat(var(--grid-col-amount), 1fr);
    .#{$prefix}col-align-right{
        text-align: right;
    }
    .#{$prefix}col-align-left{
        text-align: left;
    }
    .#{$prefix}col-align-center{
        text-align: center;
    }
    .#{$prefix}grid{
        padding: 0;
    }
    .#{$prefix}col-space-between-v{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }



    @for $i from 1 through 12 {
        .#{$prefix}col-#{$i} {
            grid-column: span #{$i};
        }
    }

    @for $i from 1 through 12 {
        @media (min-width: #{$breakpoint-sm}) {
            .#{$prefix}col-sm-#{$i} {
                grid-column: span #{$i};
            }
        }
    }

    @for $i from 1 through 12 {
        @media (min-width: #{$breakpoint-md}) {
            .#{$prefix}col-md-#{$i} {
                grid-column: span #{$i};
            }
        }
    }

    @for $i from 1 through 12 {
        @media (min-width: #{$breakpoint-lg}) {
            .#{$prefix}col-lg-#{$i} {
                grid-column: span #{$i};
            }
        }
    }

    @for $i from 1 through 12 {
        @media (min-width: #{$breakpoint-xl}) {
            .#{$prefix}col-xl-#{$i} {
                grid-column: span #{$i};
            }
        }
    }

    @for $i from 1 through 12 {
        @media (min-width: #{$breakpoint-xxl}) {
            .#{$prefix}col-xxl-#{$i} {
                grid-column: span #{$i};
            }
        }
    }
}

.flex {
    display: flex;
}

.vm-flex {
    display: flex;
    flex-wrap: wrap;

    small {
        width: calc(50% - 10px);
        flex-basis: calc(50% - 10px);

        &:nth-child(2n) {
            margin-left: auto;
        }
    }
}

.align-items {
    align-items: center;
}