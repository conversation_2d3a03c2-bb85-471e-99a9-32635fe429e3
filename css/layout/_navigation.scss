// Anchor position fixes
#bergets-ro-fastighetsformedling {
    padding-top: calc(65px + 33px);
    margin-top: -65px;
    position: relative;
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 65px;
        background: #fff;
        pointer-events: none;
    }
    @media screen and (max-width: 991px) {
        padding-top: calc(62px + 33px);
        margin-top: -62px;
        &::before {
            height: 62px;
        }
    }

    h2 {
        @media screen and (max-width: 576px) {
            font-size: clamp(16px, 8.3vw, 36px);
        }
    }
}
#offerts {
    padding-top: calc(65px + 60px);
    margin-top: -65px;
    position: relative;
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 65px;
        pointer-events: none;
    }
    @media screen and (max-width: 991px) {
        padding-top: calc(62px + 48px);
        margin-top: -62px;
        &::before {
            height: 62px;
        }
    }
}
.home #offerts {
    padding-top: calc(65px + 47px);
    margin-top: -65px;
    position: relative;
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 65px;
        pointer-events: none;
    }
    @media screen and (max-width: 991px) {
        padding-top: calc(62px + 23px);
        margin-top: -62px;
        &::before {
            height: 62px;
        }
    }
}
#contact {
    padding-top: calc(65px + 33px);
    margin-top: -65px;
    position: relative;
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 65px;
        pointer-events: none;
    }
    @media screen and (max-width: 991px) {
        padding-top: calc(62px + 33px);
        margin-top: -62px;
        &::before {
            height: 62px;
        }
    }
}
.home #contact {
    padding-top: calc(65px + 50px);
    margin-top: -65px;
    &::before {
        background-color: #fff;
        height: 65px;
    }
    @media(max-width: $screen-md) {
        padding-top: calc(62px + 30px);
        margin-top: -62px;
        &::before {
            height: 62px;
        }
    }
}
.single-houses #contact {
    padding-top: calc(65px + 60px)!important;
    margin-top: -65px!important;
    position: relative;
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 65px;
        pointer-events: none;
        background: #fff;
    }
    @media screen and (max-width: 991px) {
        padding-top: calc(62px + 40px)!important;
        margin-top: -62px!important;
        &::before {
            height: 62px;
        }
    }
}

#tab1,
#tab2,
#tab3,
#tab4,
#tab5,
#tab6 {
    padding-top: calc(65px + 115px + 50px);
    margin-top: calc(-65px - 115px - 50px);
    position: relative;
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: calc(65px + 115px);
        pointer-events: none;
    }
    @media screen and (max-width: 991px) {
        padding-top: calc(62px + 33px);
        margin-top: calc(-62px - 33px);
        &::before {
            height: 62px;
        }
    }
}
// fix for previous sections
.vm-grid:first-child,
.vm-single-offerts__table__viewings,
.vm-single-offerts__bids {
    position: relative;
    z-index: 2;
}
.tablink-mobile {
    position: relative;
    z-index: 3;
}

.vm-single-offerts__table__tabs {
    @media screen and (max-width: 991px) {
        padding-top: 62px;
        margin-top: -62px;
        &::before {
            height: 62px;
        }
    }
}

// property detail tabs - mobile
.vm-header__menu .menu-item {
    &.mobile {
        display: none;
        @media(max-width: $screen-lg) {
            display: inline;
        }
    }
    &.desktop {
        @media(max-width: $screen-lg) {
            display: none;
        }
    }
}

.mobile-only {
    @media(min-width: $screen-xl) {
        display: none;
    }
}
.desktop-only {
    @media(max-width: $screen-lg) {
        display: none;
    }
}