@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

html,
body {
    overflow-x: hidden;
}

.vm-header{
    padding: 20px 0;
    z-index: 99; // 99999999
    position: fixed;
    width: 100%;
    transition: 0.2s;
    top: 0;
    @media(min-width: $screen-lg){
        padding: 21px 0;
    }
    & > .vm-grid {
        // padding: 0;
        &--alt {
            display: none;
        }
    }
    &__logo{
        img, svg{
            width: 159px;
            height: 12px;
            @media(min-width: $screen-lg){
                width: 273px;
                height: 22px;
            }
        }
        &--alt {
            padding: 0;
            margin-top: -1px;
            margin-bottom: -1px;
            img, svg{
                width: 201.39px;
                height: 24px;
            }
        }
    }
    &__menu{
        text-align: right;
        .menu-item {
            a{
                color: get_color('font', 1);
                font-size: 15px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                text-transform: uppercase;
                text-decoration: unset;
            }
            a, button {
                @media(max-width: $screen-lg){
                    line-height: 1.8;
                }
            }
            &:not(:last-of-type){
                @media(min-width: $screen-lg){
                    margin-right: 40px;
                }
                @media(min-width: $screen-xxl){
                    margin-right: 60px;
                }
            }
            display: inline-block;
        }
        display: none;
        @media(min-width: $screen-lg){
            display: block;
        }

        .vm-header__menu-close{
            display: none;
        }
        &.active{
            
            z-index: 999;
            visibility: visible;
            display: block;
            transition: .5s ease-in-out;
            .menu-item{
                &:first-of-type{
                    margin-top: 150px;
                }
                a{
                    color: get_color('font', 6);
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    text-transform: none;
                    text-decoration: unset;
                }
                display: block;
                text-align: center;
                margin-bottom: 16px;
            }
            .vm-header__menu-close{
                cursor: pointer;
                font-size: 30px;
                color: get_color('font', 6);
                line-height: 0.5;
                display: inline-block;
            }
        }





        &-logo{
             @media(min-width: $screen-lg){
                display: none;
            }
            & > .vm-grid {
                padding: 0;
            }
        }
        &--mobile {
            visibility: hidden;
            display: block;
            opacity: 0;
            transition: .4s ease-in-out;
            padding: 40px;
            position: fixed;
            inset: 0;
            background-color: get_color('bg', 4);
            @media(min-width: $screen-lg){
                display: none;
            }
            &.active {
                opacity: 1;
            }
        }

    }

    &__icons{
        text-align: right;
        .vm-header__search{
            height: 21px;
            width: 21px;
            display: inline-block;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjMiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMyAyMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOC41OTA5MSIgY3k9IjguNTkwOTEiIHI9IjcuNTkwOTEiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxsaW5lIHgxPSIxNS4zNDM1IiB5MT0iMTMuOTI5MiIgeDI9IjIxLjcwNzEiIHkyPSIyMC4yOTI5IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            &.active{
                background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE5Ljc1IDEyQzE5Ljc1IDEyLjQxNCAxOS40MTQgMTIuNzUgMTkgMTIuNzVIMTIuNzVWMTlDMTIuNzUgMTkuNDE0IDEyLjQxNCAxOS43NSAxMiAxOS43NUMxMS41ODYgMTkuNzUgMTEuMjUgMTkuNDE0IDExLjI1IDE5VjEyLjc1SDVDNC41ODYgMTIuNzUgNC4yNSAxMi40MTQgNC4yNSAxMkM0LjI1IDExLjU4NiA0LjU4NiAxMS4yNSA1IDExLjI1SDExLjI1VjVDMTEuMjUgNC41ODYgMTEuNTg2IDQuMjUgMTIgNC4yNUMxMi40MTQgNC4yNSAxMi43NSA0LjU4NiAxMi43NSA1VjExLjI1SDE5QzE5LjQxNCAxMS4yNSAxOS43NSAxMS41ODYgMTkuNzUgMTJaIiBmaWxsPSIjZmZmIi8+Cjwvc3ZnPgo=');
                transform: rotate(45deg);
                background-size: 30px;
            }
        }
        .vm-header__hamburger{
            cursor: pointer;
            vertical-align: super;
            height: 10px;
            width: 19px;
            display: inline-block;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxOSAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGxpbmUgeTE9IjEiIHgyPSIxOSIgeTI9IjEiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxsaW5lIHkxPSI2IiB4Mj0iMTkiIHkyPSI2IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMiIvPgo8bGluZSB5MT0iMTEiIHgyPSIxOSIgeTI9IjExIiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            margin-left: 18px;
            position: relative;
            top: 2px;
            @media(min-width: $screen-lg){
                display: none;
            }
        }
    }
    &__search-form {
        position: absolute;
        right: 70px;
        top: -14px;
        display: block; // Keep it as block, but invisible when not active
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.5s ease; // Transition for both fade-in and fade-out
        @media (max-width: 991px) {
            top: 45px;
            right: 10px;
        }
        @media (max-width: 767px) {
            right: 15px;
        }
        @media (max-width: 575px) {
            left: 15px;
        }
    
        &.active {
            visibility: visible;
            opacity: 1; // Only change opacity when active
        }
    


        .asl_w_container{
            min-width: 420px;
            max-width: 100%;

            @media (max-width: 575px) {
                min-width: auto;
            }
        }


    }

    &__halfmoon {
        display: block;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);

        @media(max-width: $screen-xs) {
            display: none;
        }
    }
    .single-houses &__halfmoon {
        display: none;
    }

    .vm-button {
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        text-transform: uppercase;
        text-decoration: unset;
        padding: 6px 12px;
        top: -5px;
        position: relative;
        font-family: "Kunst Grotesk";
        @media(max-width: $screen-lg){
            padding: 0;
            top: auto;
            border: none;
            &:hover {
                background: none;
                color: get_color('font', 5);
            }
        }
        & + .vm-button {
            margin-left: 10px;
        }
    }
}

// Adjust team member image in search results
div[id*='ajaxsearchliteres'].wpdreams_asl_results .results .asl_r_team img.asl_image {
    object-fit: contain;
}

// Scroll
.scrolled {
    .vm-header{
        background-color: get_color('bg', 1);
        &__logo {
            svg {
                path {
                    fill: #000;
                }
            }
        }
        @media (min-width: 992px){
            &__menu{
                .menu-item {
                    a{
                        color: get_color('font', 5);
                    }
                }
            }
        }
        &__icons{
            text-align: right;
            .vm-header__search{
                background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjMiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMyAyMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOC41OTA5MSIgY3k9IjguNTkwOTEiIHI9IjcuNTkwOTEiIHN0cm9rZT0iYmxhY2siIHN0cm9rZS13aWR0aD0iMiIvPgo8bGluZSB4MT0iMTUuMzQzNSIgeTE9IjEzLjkyOTIiIHgyPSIyMS43MDcxIiB5Mj0iMjAuMjkyOSIgc3Ryb2tlPSJibGFjayIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjwvc3ZnPgo=');
                &.active{
                    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE5Ljc1IDEyQzE5Ljc1IDEyLjQxNCAxOS40MTQgMTIuNzUgMTkgMTIuNzVIMTIuNzVWMTlDMTIuNzUgMTkuNDE0IDEyLjQxNCAxOS43NSAxMiAxOS43NUMxMS41ODYgMTkuNzUgMTEuMjUgMTkuNDE0IDExLjI1IDE5VjEyLjc1SDVDNC41ODYgMTIuNzUgNC4yNSAxMi40MTQgNC4yNSAxMkM0LjI1IDExLjU4NiA0LjU4NiAxMS4yNSA1IDExLjI1SDExLjI1VjVDMTEuMjUgNC41ODYgMTEuNTg2IDQuMjUgMTIgNC4yNUMxMi40MTQgNC4yNSAxMi43NSA0LjU4NiAxMi43NSA1VjExLjI1SDE5QzE5LjQxNCAxMS4yNSAxOS43NSAxMS41ODYgMTkuNzUgMTJaIiBmaWxsPSIjMjUzMTRDIi8+Cjwvc3ZnPgo=');
                }
            }
            .vm-header__hamburger{
                background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxOSAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGxpbmUgeTE9IjEiIHgyPSIxOSIgeTI9IjEiIHN0cm9rZT0iYmxhY2siIHN0cm9rZS13aWR0aD0iMiIvPgo8bGluZSB5MT0iNiIgeDI9IjE5IiB5Mj0iNiIgc3Ryb2tlPSJibGFjayIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxsaW5lIHkxPSIxMSIgeDI9IjE5IiB5Mj0iMTEiIHN0cm9rZT0iYmxhY2siIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K');
            }
        }

        &__halfmoon {
            svg {
                path {
                    fill: #000;
                }
            }
        }
    }
}

// Scroll on property page
.single-houses.scrolled {
    .vm-header {
        @media(max-width: $screen-lg){
            padding: 15px 20px;
        }
        & > .vm-grid {
            display: none;
            @media(max-width: $screen-xs){
                padding-left: 0;
                padding-right: 0;
            }
            &--alt {
                display: grid;
                .vm-header__menu{
                    height: 19px;
                    overflow: visible;
                    display: block;
                    position: relative;
                    top: -2px;
                    @media(max-width: $screen-xl){
                        top: -1px;
                        text-align: center;
                    }
                    @media(max-width: $screen-lg){
                        top: auto;
                        height: auto;
                    }
                    .menu-item {
                        a{
                            color: get_color('font', 5);
                        }
                        &:not(:last-of-type){
                            margin-right: 30px;
                            @media(max-width: $screen-xl){
                                margin-right: 15px;
                            }
                        }
                    }
                    .menu-item-btn {
                        &:not(:last-of-type){
                            margin-right: 10px;
                            @media(max-width: $screen-lg){
                                margin-right: 10px;
                            }
                        }
                    }
                }
            }
            & > div:first-child {
                @media(max-width: $screen-xl){
                    display: none;
                }
            }
        }
    }

    // Hide search form on scroll
    .wpdreams_asl_results {
        display: none!important;
    }
}

// Admin bar fix
.admin-bar {
    header.vm-header {
        top: 32px;
        @media (max-width: 782px){
            top: 46px;
        }
    }
}
.admin-bar.scrolled {
    header.vm-header {
        @media (max-width: 600px){
            top: 0;
        }
    }
}

.wpdreams_asl_results{
    z-index: 99999999999999 !important;
}

.vm-header__menu.active .menu-item a {
    font-size: 32px;
    text-transform: uppercase;
    text-align: center;
}

// Hovers
@media (min-width: 992px) {
    .vm-header__menu .menu-item a {
		transition: 0.2s;
		position: relative;
		overflow: hidden;
		display: block;
    }
	.vm-header__menu .menu-item a::after {
		content: '';
		display: block;
		position: absolute;
		bottom: 0;
		border-bottom: 1px solid #fff;
		width: 100%;
		transform: translatex(-100%);
	    transition: 0.2s;
	}
    .scrolled .vm-header__menu .menu-item a::after {
        border-bottom: 1px solid #000;
    }
	.vm-header__menu .menu-item a:hover::after  {
		transform: translatex(0)
	}
}

// Ajax search - changes
#ajaxsearchlite1 .probox .proinput input, 
div.asl_w .probox .proinput input {
    font-size: 14px!important;
}
.asl_nores_header {
    font-size: 12px!important;
}