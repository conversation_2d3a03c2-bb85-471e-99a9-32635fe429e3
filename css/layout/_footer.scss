.vm-footer{
    background-color: get_color('bg', 4);
    padding: 40px 20px 33px 20px;
    @media(max-width: $screen-md) {
        padding: 35px 0 23px 0;
    }
    a {
        color: get_color('font', 6);
        text-decoration: unset;
        &:hover{
            text-decoration: underline;
        }
    }
    &__logo{
        img{
            width: 273px;
            height: 22px;
        }
        margin-bottom: 30px;
        //@media(min-width: $screen-lg){
        //    margin-bottom: 406px;
        //}
    }
    &__address{
        color: get_color('font', 1);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 120px;
        @media(min-width: $screen-md) {
            font-size: 17px;
        }
        @media(min-width: $screen-lg){
            font-size: 18px;
        }
        @media(min-width: $screen-xl) {
            font-size: 19px;
        }
    }
    &__bottom-logo{
        img{
            width:192px;
            height: 22px;
        }
    }
    &__menu{
        .menu-item{
            a{
                color: get_color('font', 6);
                font-size: 15px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                text-transform: uppercase;
                text-decoration: unset;
                @media(max-width: $screen-lg) {
                    display: block;
                    padding: 8px 0;
                }
                &:hover{
                    text-decoration: underline;
                }
            }
            &:not(:last-of-type){
                margin-right: 40px;
                @media(min-width: $screen-xxl){
                    margin-right: 60px;
                }
            }
            display: inline-block;
        }
        .hidden-mobile {
            @media(max-width: $screen-lg) {
                display: none;
            }
        }
    }
    .vm-col-align-right {
        @media(max-width: $screen-lg) {
            margin-top: 15px;
            text-align: left;
        }
        @media(max-width: $screen-sm) {
            margin-top: 5px;
        }
    }
}