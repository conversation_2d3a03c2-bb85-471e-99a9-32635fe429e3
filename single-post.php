<?php
get_header();
?>

<main id="primary" class="site-main">

    <?php if (have_posts()) : while (have_posts()) : the_post(); ?>

            <?php
            $featured_img_url = get_the_post_thumbnail_url(get_the_ID(), 'full');
            $excerpt = get_the_excerpt();
            ?>

            <section class="single-post-hero" style="background-image: url('<?php echo esc_url($featured_img_url); ?>');">
                <div class="single-post-hero__overlay">
                    <div class="single-post-hero__content">

                        <section class="single-post-meta container">
                            <div class="single-post-meta__content">
                                <span class="meta-item"><?php echo get_the_date(); ?></span>
                                <span class="meta-separator">•</span>
                                <span class="meta-item"><?php echo reading_time(); ?> min lästid</span>
                                <span class="meta-separator">•</span>
                                <span class="meta-item"><?php the_author(); ?></span>
                            </div>
                        </section>

                        <h1 class="single-post-hero__title"><?php the_title(); ?></h1>
                        <?php if ($excerpt) : ?>
                            <p class="single-post-hero__excerpt"><?php echo esc_html($excerpt); ?></p>
                        <?php endif; ?>

                        <a href="/salja" class="single-post-hero__cta">Kontakta oss</a>

                    </div>
                </div>
            </section>

            <section class="single-post-content container">
                <article id="post-<?php the_ID(); ?>" <?php post_class('single-post'); ?>>
                    <div class="single-post__text">
                        <?php the_content(); ?>
                    </div>
                </article>
            </section>

            <section class="related-posts container">
                <h2 class="related-posts__title">Relaterade inlägg</h2>

                <?php
                $categories = wp_get_post_categories(get_the_ID());
                if ($categories) {
                    $args = array(
                        'category__in' => $categories,
                        'post__not_in' => array(get_the_ID()),
                        'posts_per_page' => 3,
                        'ignore_sticky_posts' => 1,
                    );
                    $related_posts = new WP_Query($args);

                    if ($related_posts->have_posts()) :
                        echo '<div class="blog-grid">';
                        while ($related_posts->have_posts()) : $related_posts->the_post();
                ?>
                            <article id="post-<?php the_ID(); ?>" <?php post_class('blog-card'); ?>>
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="blog-card__image">
                                        <a href="<?php the_permalink(); ?>">
                                            <?php the_post_thumbnail('medium_large', ['loading' => 'lazy']); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <div class="blog-card__content">
                                    <div class="blog-card__categories">
                                        <?php
                                        $post_categories = get_the_category();
                                        if (!empty($post_categories)) :
                                            foreach ($post_categories as $cat) :
                                        ?>
                                                <a href="<?php echo esc_url(get_category_link($cat->term_id)); ?>"
                                                    class="blog-card__category cat-<?php echo esc_attr($cat->slug); ?>">
                                                    <?php echo esc_html($cat->name); ?>
                                                </a>
                                        <?php endforeach;
                                        endif;
                                        ?>
                                    </div>

                                    <h2 class="blog-card__title">
                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                    </h2>
                                    <div class="blog-card__meta">
                                        <?php the_time('F j, Y'); ?> • <?php the_author(); ?>
                                    </div>
                                    <div class="blog-card__excerpt">
                                        <?php the_excerpt(); ?>
                                    </div>
                                    <a class="blog-card__readmore" href="<?php the_permalink(); ?>">Läs mer →</a>
                                </div>
                            </article>
                <?php
                        endwhile;
                        echo '</div>';
                    endif;
                    wp_reset_postdata();
                }
                ?>
            </section>

    <?php endwhile;
    endif; ?>

</main>

<?php
get_footer();
