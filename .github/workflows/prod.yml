on: 
  push:
    branches:
      - main
name: 🚀 Deploy website to PROD on push
jobs:
  web-deploy:
    name: 🎉 Deploy to PROD
    runs-on: ubuntu-latest
    
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v3

    - name: Use Node.js 16
      uses: actions/setup-node@v2
      with:
        node-version: '16'
      
    - name: 🔨 Build Project
      run: |
        npm install
        npm run build

    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@v4.3.4
      with:
        server: bergetsro.se
        username: bergetsro_ftp
        password: ${{ secrets.ftp_password }}
        server-dir: /web/wp-content/themes/bergets-theme/